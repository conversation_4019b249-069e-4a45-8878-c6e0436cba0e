package com.hl.orasync.convert;

import com.hl.archive.domain.entity.PolicePositionRank;
import com.hl.orasync.domain.VWjRyzwzj;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-02T16:34:00+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
public class PolicePositionRankConvertImpl implements PolicePositionRankConvert {

    @Override
    public PolicePositionRank convertPolicePositionRank(VWjRyzwzj vwjRyzwzj) {
        if ( vwjRyzwzj == null ) {
            return null;
        }

        PolicePositionRank policePositionRank = new PolicePositionRank();

        policePositionRank.setPositionName( vwjRyzwzj.getZwmc() );
        policePositionRank.setPolicePositionLevel( vwjRyzwzj.getGazwjb() );
        try {
            if ( vwjRyzwzj.getZwsxsj() != null ) {
                policePositionRank.setCurrentPositionDate( new SimpleDateFormat().parse( vwjRyzwzj.getZwsxsj() ) );
            }
        }
        catch ( ParseException e ) {
            throw new RuntimeException( e );
        }
        try {
            if ( vwjRyzwzj.getXzjsj() != null ) {
                policePositionRank.setCurrentRankDate( new SimpleDateFormat().parse( vwjRyzwzj.getXzjsj() ) );
            }
        }
        catch ( ParseException e ) {
            throw new RuntimeException( e );
        }
        policePositionRank.setAppointmentDocument( vwjRyzwzj.getRzwh() );
        policePositionRank.setIdCard( vwjRyzwzj.getGmsfhm() );

        return policePositionRank;
    }
}
