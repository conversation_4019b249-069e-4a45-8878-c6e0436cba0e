package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceMomentSubmissionVideo;
import com.hl.archive.domain.entity.PoliceMomentSubmissionVideoToVWjJcsjScsbWjMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__257;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__257.class,
    uses = {ConversionUtils.class,PoliceMomentSubmissionVideoToVWjJcsjScsbWjMapper.class},
    imports = {}
)
public interface VWjJcsjScsbWjToPoliceMomentSubmissionVideoMapper extends BaseMapper<VWjJcsjScsbWj, PoliceMomentSubmissionVideo> {
  @Mapping(
      target = "fileName",
      source = "wjmc"
  )
  @Mapping(
      target = "sbZjbh",
      source = "sbXxzjbh"
  )
  @Mapping(
      target = "fileUrl",
      source = "fjcl2"
  )
  @Mapping(
      target = "fileType",
      source = "wjlxmc"
  )
  @Mapping(
      target = "zjbh",
      source = "xxzjbh"
  )
  PoliceMomentSubmissionVideo convert(VWjJcsjScsbWj source);

  @Mapping(
      target = "fileName",
      source = "wjmc"
  )
  @Mapping(
      target = "sbZjbh",
      source = "sbXxzjbh"
  )
  @Mapping(
      target = "fileUrl",
      source = "fjcl2"
  )
  @Mapping(
      target = "fileType",
      source = "wjlxmc"
  )
  @Mapping(
      target = "zjbh",
      source = "xxzjbh"
  )
  PoliceMomentSubmissionVideo convert(VWjJcsjScsbWj source,
      @MappingTarget PoliceMomentSubmissionVideo target);
}
