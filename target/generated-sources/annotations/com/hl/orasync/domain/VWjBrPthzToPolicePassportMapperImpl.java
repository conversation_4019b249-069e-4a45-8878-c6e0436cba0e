package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PolicePassport;
import com.hl.orasync.util.ConversionUtils;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-02T16:34:01+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class VWjBrPthzToPolicePassportMapperImpl implements VWjBrPthzToPolicePassportMapper {

    @Override
    public PolicePassport convert(VWjBrPthz source) {
        if ( source == null ) {
            return null;
        }

        PolicePassport policePassport = new PolicePassport();

        policePassport.setExpiryDate( ConversionUtils.strToDate( source.getYxqz() ) );
        policePassport.setPassportNumber( source.getHzhm() );
        policePassport.setIdCard( source.getGmsfhm() );
        policePassport.setIssueDate( ConversionUtils.strToDate( source.getQfrq() ) );
        policePassport.setRemarks( source.getBz() );
        policePassport.setCustodyOrganization( source.getBgjgmc() );

        return policePassport;
    }

    @Override
    public PolicePassport convert(VWjBrPthz source, PolicePassport target) {
        if ( source == null ) {
            return target;
        }

        target.setExpiryDate( ConversionUtils.strToDate( source.getYxqz() ) );
        target.setPassportNumber( source.getHzhm() );
        target.setIdCard( source.getGmsfhm() );
        target.setIssueDate( ConversionUtils.strToDate( source.getQfrq() ) );
        target.setRemarks( source.getBz() );
        target.setCustodyOrganization( source.getBgjgmc() );

        return target;
    }
}
