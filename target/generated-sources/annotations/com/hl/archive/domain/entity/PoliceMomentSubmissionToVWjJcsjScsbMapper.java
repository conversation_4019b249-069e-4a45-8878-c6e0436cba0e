package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjJcsjScsb;
import com.hl.orasync.domain.VWjJcsjScsbToPoliceMomentSubmissionMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__257;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__257.class,
    uses = {ConversionUtils.class,VWjJcsjScsbToPoliceMomentSubmissionMapper.class},
    imports = {}
)
public interface PoliceMomentSubmissionToVWjJcsjScsbMapper extends BaseMapper<PoliceMomentSubmission, VWjJcsjScsb> {
  @Mapping(
      target = "shjgmc",
      source = "auditResult"
  )
  @Mapping(
      target = "bslxmc",
      source = "submissionType"
  )
  @Mapping(
      target = "djsj",
      source = "submissionTime"
  )
  @Mapping(
      target = "bsdwmc",
      source = "submitUnit"
  )
  @Mapping(
      target = "scjj",
      source = "materialIntro"
  )
  @Mapping(
      target = "cjmj",
      source = "officerName"
  )
  @Mapping(
      target = "bsrxm",
      source = "submitter"
  )
  @Mapping(
      target = "xxzjbh",
      source = "zjbh"
  )
  @Mapping(
      target = "cllxmc",
      source = "materialType"
  )
  VWjJcsjScsb convert(PoliceMomentSubmission source);

  @Mapping(
      target = "shjgmc",
      source = "auditResult"
  )
  @Mapping(
      target = "bslxmc",
      source = "submissionType"
  )
  @Mapping(
      target = "djsj",
      source = "submissionTime"
  )
  @Mapping(
      target = "bsdwmc",
      source = "submitUnit"
  )
  @Mapping(
      target = "scjj",
      source = "materialIntro"
  )
  @Mapping(
      target = "cjmj",
      source = "officerName"
  )
  @Mapping(
      target = "bsrxm",
      source = "submitter"
  )
  @Mapping(
      target = "xxzjbh",
      source = "zjbh"
  )
  @Mapping(
      target = "cllxmc",
      source = "materialType"
  )
  VWjJcsjScsb convert(PoliceMomentSubmission source, @MappingTarget VWjJcsjScsb target);
}
