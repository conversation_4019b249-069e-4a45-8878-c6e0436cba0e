package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjXlda;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-02T16:34:01+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class PoliceTrainingRecordsToVWjXldaMapperImpl implements PoliceTrainingRecordsToVWjXldaMapper {

    @Override
    public VWjXlda convert(PoliceTrainingRecords source) {
        if ( source == null ) {
            return null;
        }

        VWjXlda vWjXlda = new VWjXlda();

        vWjXlda.setPxbmc( source.getTrainingName() );
        vWjXlda.setGmsfhm( source.getIdCard() );
        vWjXlda.setCj( source.getGrade() );
        vWjXlda.setKpxmmc( source.getExamProjectName() );
        try {
            if ( source.getTrainingTime() != null ) {
                vWjXlda.setPxsj( new SimpleDateFormat().parse( source.getTrainingTime() ) );
            }
        }
        catch ( ParseException e ) {
            throw new RuntimeException( e );
        }
        vWjXlda.setPfdf( source.getScore() );

        return vWjXlda;
    }

    @Override
    public VWjXlda convert(PoliceTrainingRecords source, VWjXlda target) {
        if ( source == null ) {
            return target;
        }

        target.setPxbmc( source.getTrainingName() );
        target.setGmsfhm( source.getIdCard() );
        target.setCj( source.getGrade() );
        target.setKpxmmc( source.getExamProjectName() );
        try {
            if ( source.getTrainingTime() != null ) {
                target.setPxsj( new SimpleDateFormat().parse( source.getTrainingTime() ) );
            }
            else {
                target.setPxsj( null );
            }
        }
        catch ( ParseException e ) {
            throw new RuntimeException( e );
        }
        target.setPfdf( source.getScore() );

        return target;
    }
}
