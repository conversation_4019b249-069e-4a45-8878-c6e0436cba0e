package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjBrHsjq;
import com.hl.orasync.domain.VWjBrHsjqToPoliceWeddingFuneralEventsMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__257;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__257.class,
    uses = {ConversionUtils.class,VWjBrHsjqToPoliceWeddingFuneralEventsMapper.class},
    imports = {}
)
public interface PoliceWeddingFuneralEventsToVWjBrHsjqMapper extends BaseMapper<PoliceWeddingFuneralEvents, VWjBrHsjq> {
  @Mapping(
      target = "cbrq",
      source = "eventDate"
  )
  @Mapping(
      target = "gxDsr",
      source = "relationshipWithParty"
  )
  @Mapping(
      target = "xmDsr",
      source = "partyName"
  )
  @Mapping(
      target = "cblxmc",
      source = "eventType"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "cyrs",
      source = "participantCount"
  )
  @Mapping(
      target = "je",
      source = "expenseAmount"
  )
  VWjBrHsjq convert(PoliceWeddingFuneralEvents source);

  @Mapping(
      target = "cbrq",
      source = "eventDate"
  )
  @Mapping(
      target = "gxDsr",
      source = "relationshipWithParty"
  )
  @Mapping(
      target = "xmDsr",
      source = "partyName"
  )
  @Mapping(
      target = "cblxmc",
      source = "eventType"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "cyrs",
      source = "participantCount"
  )
  @Mapping(
      target = "je",
      source = "expenseAmount"
  )
  VWjBrHsjq convert(PoliceWeddingFuneralEvents source, @MappingTarget VWjBrHsjq target);
}
