package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjBrHsjq;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-02T16:34:01+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class PoliceWeddingFuneralEventsToVWjBrHsjqMapperImpl implements PoliceWeddingFuneralEventsToVWjBrHsjqMapper {

    @Override
    public VWjBrHsjq convert(PoliceWeddingFuneralEvents source) {
        if ( source == null ) {
            return null;
        }

        VWjBrHsjq vWjBrHsjq = new VWjBrHsjq();

        if ( source.getEventDate() != null ) {
            vWjBrHsjq.setCbrq( new SimpleDateFormat().format( source.getEventDate() ) );
        }
        vWjBrHsjq.setGxDsr( source.getRelationshipWithParty() );
        vWjBrHsjq.setXmDsr( source.getPartyName() );
        vWjBrHsjq.setCblxmc( source.getEventType() );
        vWjBrHsjq.setGmsfhm( source.getIdCard() );
        if ( source.getParticipantCount() != null ) {
            vWjBrHsjq.setCyrs( BigDecimal.valueOf( source.getParticipantCount() ) );
        }
        if ( source.getExpenseAmount() != null ) {
            vWjBrHsjq.setJe( source.getExpenseAmount().toString() );
        }

        return vWjBrHsjq;
    }

    @Override
    public VWjBrHsjq convert(PoliceWeddingFuneralEvents source, VWjBrHsjq target) {
        if ( source == null ) {
            return target;
        }

        if ( source.getEventDate() != null ) {
            target.setCbrq( new SimpleDateFormat().format( source.getEventDate() ) );
        }
        else {
            target.setCbrq( null );
        }
        target.setGxDsr( source.getRelationshipWithParty() );
        target.setXmDsr( source.getPartyName() );
        target.setCblxmc( source.getEventType() );
        target.setGmsfhm( source.getIdCard() );
        if ( source.getParticipantCount() != null ) {
            target.setCyrs( BigDecimal.valueOf( source.getParticipantCount() ) );
        }
        else {
            target.setCyrs( null );
        }
        if ( source.getExpenseAmount() != null ) {
            target.setJe( source.getExpenseAmount().toString() );
        }
        else {
            target.setJe( null );
        }

        return target;
    }
}
