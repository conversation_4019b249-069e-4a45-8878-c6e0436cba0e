package com.hl.archive.enums;

/**
 * 人员预警类别枚举
 */
public enum PersonWarnTypeEnum {
    /** 警情 */
    POLICE_SITUATION("jq", "警情"),
    /** 案件 */
    CASE("aj", "案件"),
    /** 信访 */
    PETITION("xf", "信访");

    private final String code;
    private final String label;

    PersonWarnTypeEnum(String code, String label) {
        this.code = code;
        this.label = label;
    }

    public String getCode() {
        return code;
    }

    public String getLabel() {
        return label;
    }
} 