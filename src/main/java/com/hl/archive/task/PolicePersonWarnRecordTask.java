package com.hl.archive.task;

import cn.hutool.core.date.DateUtil;
import cn.hutool.db.DbUtil;
import cn.hutool.db.Entity;
import com.aizuda.snailjob.client.job.core.annotation.JobExecutor;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hl.archive.domain.entity.PoliceBasicInfo;
import com.hl.archive.domain.entity.PolicePersonWarnRecord;
import com.hl.archive.service.PoliceBasicInfoService;
import com.hl.archive.service.PolicePersonWarnRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class PolicePersonWarnRecordTask {

    private final PolicePersonWarnRecordService policePersonWarnRecordService;

    private final PoliceBasicInfoService policeBasicInfoService;

    @Resource
    @Qualifier("datasource1DataSource")
    private DataSource jqDataSource;

    @JobExecutor(name = "refreshPersonWarnRecord")
    public void refreshPersonWarnRecord() {
        try {
            List<PoliceBasicInfo> list = policeBasicInfoService.list();
            for (PoliceBasicInfo basicInfo : list) {
                String idCard = basicInfo.getIdCard();
                List<Entity> query = DbUtil.use(jqDataSource).query("select jjbh from wjsc_jq_sjxx where gmsfhm = '" + idCard + "'");
                if (query.isEmpty()) {
                    continue;
                }
                String jjbh = query.stream().map(s -> "'" + s.getStr("jjbh") + "'").collect(Collectors.joining(","));
                List<Entity> jqData = DbUtil.use(jqDataSource).query("select * from wjsc_jq_jjxx where   jjbh in (" + jjbh + ")");
                for (Entity jqDatum : jqData) {
                    PolicePersonWarnRecord one = policePersonWarnRecordService.getOne(Wrappers.<PolicePersonWarnRecord>lambdaQuery()
                            .eq(PolicePersonWarnRecord::getIdCard, idCard)
                            .eq(PolicePersonWarnRecord::getWarnType, "jq")
                            .eq(PolicePersonWarnRecord::getDataKey, jqDatum.getStr("jjbh"))
                            .last(" limit 1"));
                    if (one != null) {
                        continue;
                    }
                    PolicePersonWarnRecord warnRecord = new PolicePersonWarnRecord();
                    warnRecord.setIdCard(idCard);
                    warnRecord.setName(basicInfo.getName());
                    warnRecord.setPoliceNumber(basicInfo.getPoliceNumber());
                    warnRecord.setOrganizationId(basicInfo.getOrganizationId());
                    warnRecord.setWarnTime(DateUtil.parse(jqDatum.getStr("bjdhsj_time")));
                    warnRecord.setWarnType("jq");
                    warnRecord.setDataKey(jqDatum.getStr("jjbh"));
                    warnRecord.setDescription(jqDatum.getStr("bjnr"));
                    policePersonWarnRecordService.save(warnRecord);
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

    }
}
