package com.hl.archive.domain.dto;

import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hl.archive.utils.TransConstants;
import com.hl.translation.annotation.Translation;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class PoliceHonorResponseDTO {

    private List< String> idCards;

    private String tagType;

    private String tagName;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date awardDate;

    @Translation(type = TransConstants.ID_CARDS_TO_USER_LIST, mapper = "idCards")
    private List<JSONObject> personInfoList;

    private Integer sourceType;
}
