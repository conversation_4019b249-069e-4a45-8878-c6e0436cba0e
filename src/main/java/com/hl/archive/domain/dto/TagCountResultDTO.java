package com.hl.archive.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public  class TagCountResultDTO {


    private String tagType;

    private String tagTypeName;

    private List<TagDetail> tagNameList;


    @Data
    public static class TagDetail{
        private  String tagName;

        @JsonFormat(pattern = "yyyy-MM-dd")
        private Date awardDate;
    }

}
