package com.hl.archive.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("民警质态统计")
public class PoliceWorkQualityCountReturnDTO {


    @ApiModelProperty("主办案件数量")
    private Long zbajCount = 0L;

    @ApiModelProperty("协办案件数量")
    private Long xbajCount = 0L;

    @ApiModelProperty("刑事案件统计")
    private XsCaseStat xsCaseStat ;

    @ApiModelProperty("治安案件统计")
    private ZaCaseStat zaCaseStat;

    @ApiModelProperty("案件考评")
    private Long caseExamineCount = 0L;

    @ApiModelProperty("警情考评")
    private Long policeExamineCount = 0L;

    @ApiModelProperty("办案场所考评")
    private Long casePlaceExamineCount = 0L;

    @ApiModelProperty("签转超时")
    private Long signTimeoutCount = 0L;

    @ApiModelProperty("办结超时")
    private Long workTimeoutCount = 0L;

    @ApiModelProperty("处警数量")
    private Long handlePoliceCount = 0L;

    @Data
    public static class XsCaseStat {

        // 刑事传唤
        @ApiModelProperty("刑事传唤")
        private Long xschCount = 0L;

        // 监视居住
        @ApiModelProperty("监视居住")
        private Long jsjzCount = 0L;

        // 刑事拘留
        @ApiModelProperty("刑事拘留")
        private Long xsjlCount = 0L;

        // 取保候审
        @ApiModelProperty("取保候审")
        private Long qbhsCount = 0L;

        // 逮捕
        @ApiModelProperty("逮捕")
        private Long dbCount = 0L;

        // 提请起诉
        @ApiModelProperty("提请起诉")
        private Long tqqsCount = 0L;

        // 移送直诉
        @ApiModelProperty("移送直诉")
        private Long yszsCount = 0L;

    }

    @Data
    public static class ZaCaseStat {
        // 行政拘留
        @ApiModelProperty("行政拘留")
        private Long xzjlCount = 0L;

        // 行政拘留
        @ApiModelProperty("行政拘留")
        private Long chCount = 0L;

        // 警告
        @ApiModelProperty("警告")
        private Long jgCount = 0L;

        // 罚款
        @ApiModelProperty("罚款")
        private Long fkCount = 0L;

        // 罚款
        @ApiModelProperty("罚款")
        private Long bycfCount = 0L;

    }
}
