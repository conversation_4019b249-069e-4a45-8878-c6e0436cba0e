package com.hl.archive.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 健康状况表
 */
@ApiModel(description = "健康状况表")
@Data
@TableName(value = "police_health_status")
public class PoliceHealthStatus {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 身份证号
     */
    @TableField(value = "id_card")
    @ApiModelProperty(value = "身份证号")
    private String idCard;

    /**
     * 是否身患重大疾病(0:否,1:是)
     */
    @TableField(value = "has_serious_illness")
    @ApiModelProperty(value = "是否身患重大疾病(0:否,1:是)")
    private Byte hasSeriousIllness;

    /**
     * 疾病名称
     */
    @TableField(value = "illness_name")
    @ApiModelProperty(value = "疾病名称")
    private String illnessName;

    /**
     * 确诊时间
     */
    @TableField(value = "diagnosis_date")
    @ApiModelProperty(value = "确诊时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date diagnosisDate;

    /**
     * 诊断医疗机构
     */
    @TableField(value = "diagnosis_institution")
    @ApiModelProperty(value = "诊断医疗机构")
    private String diagnosisInstitution;

    /**
     * 备注
     */
    @TableField(value = "remarks")
    @ApiModelProperty(value = "备注")
    private String remarks;

    /**
     * 逻辑删除(0:未删除,1:已删除)
     */
    @TableField(value = "is_deleted")
    @ApiModelProperty(value = "逻辑删除(0:未删除,1:已删除)")
    @TableLogic
    private Byte isDeleted;

    /**
     * 创建时间
     */
    @TableField(value = "created_at")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at")
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updatedAt;

    /**
     * 创建人身份证号
     */
    @TableField(value = "created_by")
    @ApiModelProperty(value = "创建人身份证号")
    private String createdBy;

    /**
     * 更新人身份证号
     */
    @TableField(value = "updated_by")
    @ApiModelProperty(value = "更新人身份证号")
    private String updatedBy;
}