package com.hl.archive.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 配偶子女经商办企业情况表
 */
@ApiModel(description = "配偶子女经商办企业情况表")
@Data
@TableName(value = "police_family_business")
public class PoliceFamilyBusiness {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 身份证号
     */
    @TableField(value = "id_card")
    @ApiModelProperty(value = "身份证号")
    private String idCard;

    /**
     * 姓名
     */
    @TableField(value = "`name`")
    @ApiModelProperty(value = "姓名")
    private String name;

    /**
     * 统一社会信用代码/注册号
     */
    @TableField(value = "social_credit_code")
    @ApiModelProperty(value = "统一社会信用代码/注册号")
    private String socialCreditCode;

    /**
     * 企业或者其他市场主体名称
     */
    @TableField(value = "enterprise_name")
    @ApiModelProperty(value = "企业或者其他市场主体名称")
    private String enterpriseName;

    /**
     * 成立日期
     */
    @TableField(value = "establishment_date")
    @ApiModelProperty(value = "成立日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date establishmentDate;

    /**
     * 经营范围
     */
    @TableField(value = "business_scope")
    @ApiModelProperty(value = "经营范围")
    private String businessScope;

    /**
     * 注册地
     */
    @TableField(value = "registration_address")
    @ApiModelProperty(value = "注册地")
    private String registrationAddress;

    /**
     * 经营地
     */
    @TableField(value = "business_address")
    @ApiModelProperty(value = "经营地")
    private String businessAddress;

    /**
     * 企业或者其他市场主体类型
     */
    @TableField(value = "enterprise_type")
    @ApiModelProperty(value = "企业或者其他市场主体类型")
    private String enterpriseType;

    /**
     * 注册资本(金)或资金数额（出资额）
     */
    @TableField(value = "registered_capital")
    @ApiModelProperty(value = "注册资本(金)或资金数额（出资额）")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal registeredCapital;

    /**
     * 企业状态
     */
    @TableField(value = "enterprise_status")
    @ApiModelProperty(value = "企业状态")
    private String enterpriseStatus;

    /**
     * 是否为股东（合伙人、所有人）(0:否,1:是)
     */
    @TableField(value = "is_shareholder")
    @ApiModelProperty(value = "是否为股东（合伙人、所有人）(0:否,1:是)")
    private Byte isShareholder;

    /**
     * 个人认缴出资额或个人出资
     */
    @TableField(value = "personal_contribution")
    @ApiModelProperty(value = "个人认缴出资额或个人出资")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal personalContribution;

    /**
     * 个人认缴出资比例或个人出资比例
     */
    @TableField(value = "personal_contribution_ratio")
    @ApiModelProperty(value = "个人认缴出资比例或个人出资比例")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal personalContributionRatio;

    /**
     * 投资时间
     */
    @TableField(value = "investment_date")
    @ApiModelProperty(value = "投资时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date investmentDate;

    /**
     * 是否为担任高级职务(0:否,1:是)
     */
    @TableField(value = "is_senior_position")
    @ApiModelProperty(value = "是否为担任高级职务(0:否,1:是)")
    private Byte isSeniorPosition;

    /**
     * 所担任的高级职务名称
     */
    @TableField(value = "senior_position_name")
    @ApiModelProperty(value = "所担任的高级职务名称")
    private String seniorPositionName;

    /**
     * 担任高级职务的时间
     */
    @TableField(value = "senior_position_date")
    @ApiModelProperty(value = "担任高级职务的时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date seniorPositionDate;

    /**
     * 该企业或其他市场主体是否与报告人所在单位（系统）直接发生过商品、劳务、服务等经济关系(0:否,1:是)
     */
    @TableField(value = "has_business_relation")
    @ApiModelProperty(value = "该企业或其他市场主体是否与报告人所在单位（系统）直接发生过商品、劳务、服务等经济关系(0:否,1:是)")
    private Byte hasBusinessRelation;

    /**
     * 备注
     */
    @TableField(value = "remarks")
    @ApiModelProperty(value = "备注")
    private String remarks;

    /**
     * 逻辑删除(0:未删除,1:已删除)
     */
    @TableField(value = "is_deleted")
    @ApiModelProperty(value = "逻辑删除(0:未删除,1:已删除)")
    @TableLogic
    private Byte isDeleted;

    /**
     * 创建时间
     */
    @TableField(value = "created_at")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at")
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updatedAt;

    /**
     * 创建人身份证号
     */
    @TableField(value = "created_by")
    @ApiModelProperty(value = "创建人身份证号")
    private String createdBy;

    /**
     * 更新人身份证号
     */
    @TableField(value = "updated_by")
    @ApiModelProperty(value = "更新人身份证号")
    private String updatedBy;
}