package com.hl.archive.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Date;

import lombok.Data;

/**
 * 民警基本信息表
 */
@ApiModel(description = "民警基本信息表")
@Data
@TableName(value = "police_basic_info")
public class PoliceBasicInfo {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 姓名
     */
    @TableField(value = "`name`")
    @ApiModelProperty(value = "姓名")
    private String name;

    /**
     * 身份证号
     */
    @TableField(value = "id_card")
    @ApiModelProperty(value = "身份证号")
    private String idCard;

    /**
     * 性别(1:男,2:女)
     */
    @TableField(value = "gender")
    @ApiModelProperty(value = "性别(1:男,2:女)")
    private Byte gender;

    /**
     * 出生日期
     */
    @TableField(value = "birth_date")
    @ApiModelProperty(value = "出生日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date birthDate;

    /**
     * 籍贯
     */
    @TableField(value = "native_place")
    @ApiModelProperty(value = "籍贯")
    private String nativePlace;

    /**
     * 民族
     */
    @TableField(value = "nation")
    @ApiModelProperty(value = "民族")
    private String nation;

    /**
     * 警号
     */
    @TableField(value = "police_number")
    @ApiModelProperty(value = "警号")
    private String policeNumber;

    /**
     * 单位名称
     */
    @TableField(value = "unit_name")
    @ApiModelProperty(value = "单位名称")
    private String unitName;

    /**
     * 部门
     */
    @TableField(value = "department")
    @ApiModelProperty(value = "部门")
    private String department;

    /**
     * 领导职务层级
     */
    @TableField(value = "leadership_level")
    @ApiModelProperty(value = "领导职务层级")
    private String leadershipLevel;

    /**
     * 参加公安工作时间
     */
    @TableField(value = "police_work_start_date")
    @ApiModelProperty(value = "参加公安工作时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date policeWorkStartDate;

    /**
     * 参加工作时间
     */
    @TableField(value = "work_start_date")
    @ApiModelProperty(value = "参加工作时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date workStartDate;

    /**
     * 身高(cm)
     */
    @TableField(value = "height")
    @ApiModelProperty(value = "身高(cm)")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal height;

    /**
     * 体重(kg)
     */
    @TableField(value = "weight")
    @ApiModelProperty(value = "体重(kg)")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal weight;

    /**
     * 血型
     */
    @TableField(value = "blood_type")
    @ApiModelProperty(value = "血型")
    private String bloodType;

    /**
     * 逻辑删除(0:未删除,1:已删除)
     */
    @TableField(value = "is_deleted")
    @ApiModelProperty(value = "逻辑删除(0:未删除,1:已删除)")
    @TableLogic
    private Byte isDeleted;

    /**
     * 创建时间
     */
    @TableField(value = "created_at")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at")
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updatedAt;

    /**
     * 创建人身份证号
     */
    @TableField(value = "created_by")
    @ApiModelProperty(value = "创建人身份证号")
    private String createdBy;

    /**
     * 更新人身份证号
     */
    @TableField(value = "updated_by")
    @ApiModelProperty(value = "更新人身份证号")
    private String updatedBy;

    /**
     * 照片地址
     */
    @TableField(value = "img_url")
    @ApiModelProperty(value = "照片地址")
    private String imgUrl;

    @TableField(value = "education_level")
    @ApiModelProperty(value = "学历")
    private String educationLevel;

    @TableField(value = "political_identity")
    @ApiModelProperty(value = "政治面貌")
    private String politicalIdentity;

    @TableField(value = "marriage_status")
    @ApiModelProperty(value = "婚姻状况")
    private String marriageStatus;

    @TableField(value = "position_name")
    @ApiModelProperty(value = "职务职级")
    private String positionName;


    @TableField(value = "home_address")
    @ApiModelProperty(value = "家庭住址")
    private String homeAddress;

    @TableField(value = "mobile_phone")
    @ApiModelProperty(value = "手机")
    private String mobilePhone;


    @TableField(value = "organization_id")
    private String organizationId;

    @TableField(value = "duty_status")
    @ApiModelProperty(value = "在岗状态")
    private Integer dutyStatus;

}