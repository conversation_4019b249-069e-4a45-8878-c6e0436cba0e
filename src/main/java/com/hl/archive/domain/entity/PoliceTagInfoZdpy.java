package com.hl.archive.domain.entity;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.hl.archive.utils.TransConstants;
import com.hl.translation.annotation.Translation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 民警标签重点培养
 */
@ApiModel(description="民警标签重点培养")
@Data
@TableName(value = "police_tag_info_zdpy")
public class PoliceTagInfoZdpy {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="主键ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 身份证号
     */
    @TableField(value = "id_card")
    @ApiModelProperty(value="身份证号")
    private String idCard;

    /**
     * 单位ID
     */
    @TableField(value = "organization_id")
    @ApiModelProperty(value="单位ID")
    private String organizationId;

    /**
     * 获得时间
     */
    @TableField(value = "award_date")
    @ApiModelProperty(value="获得时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date awardDate;

    /**
     * 说明
     */
    @TableField(value = "remark")
    @ApiModelProperty(value="说明")
    private String remark;

    /**
     * 逻辑删除(0:未删除,1:已删除)
     */
    @TableField(value = "is_deleted")
    @TableLogic
    @ApiModelProperty(value="逻辑删除(0:未删除,1:已删除)")
    private Byte isDeleted;

    /**
     * 创建时间
     */
    @TableField(value = "created_at")
    @ApiModelProperty(value="创建时间")
    private Date createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at")
    @ApiModelProperty(value="更新时间")
    private Date updatedAt;

    /**
     * 创建人身份证号
     */
    @TableField(value = "created_by")
    @ApiModelProperty(value="创建人身份证号")
    private String createdBy;

    /**
     * 更新人身份证号
     */
    @TableField(value = "updated_by")
    @ApiModelProperty(value="更新人身份证号")
    private String updatedBy;

    @Translation(type = TransConstants.ID_CARD_TO_USER_OBJ, mapper = "idCard")
    @TableField(exist = false)
    private JSONObject personInfo;
}