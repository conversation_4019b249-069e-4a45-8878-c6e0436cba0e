package com.hl.archive.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 本人或者配偶列为失信被执行人的情况表
 */
@ApiModel(description = "本人或者配偶列为失信被执行人的情况表")
@Data
@TableName(value = "police_dishonest_executor")
public class PoliceDishonestExecutor {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 身份证号
     */
    @TableField(value = "id_card")
    @ApiModelProperty(value = "身份证号")
    private String idCard;

    /**
     * 当事人姓名
     */
    @TableField(value = "party_name")
    @ApiModelProperty(value = "当事人姓名")
    private String partyName;

    /**
     * 与当事人关系
     */
    @TableField(value = "relationship")
    @ApiModelProperty(value = "与当事人关系")
    private String relationship;

    /**
     * 被列为失信被执行人的原因
     */
    @TableField(value = "dishonest_reason")
    @ApiModelProperty(value = "被列为失信被执行人的原因")
    private String dishonestReason;

    /**
     * 执行单位名称
     */
    @TableField(value = "execution_unit")
    @ApiModelProperty(value = "执行单位名称")
    private String executionUnit;

    /**
     * 被列为失信被执行人时间
     */
    @TableField(value = "dishonest_date")
    @ApiModelProperty(value = "被列为失信被执行人时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date dishonestDate;

    /**
     * 取消被列为失信被执行人时间
     */
    @TableField(value = "cancellation_date")
    @ApiModelProperty(value = "取消被列为失信被执行人时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date cancellationDate;

    /**
     * 逻辑删除(0:未删除,1:已删除)
     */
    @TableField(value = "is_deleted")
    @ApiModelProperty(value = "逻辑删除(0:未删除,1:已删除)")
    @TableLogic
    private Byte isDeleted;

    /**
     * 创建时间
     */
    @TableField(value = "created_at")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at")
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updatedAt;

    /**
     * 创建人身份证号
     */
    @TableField(value = "created_by")
    @ApiModelProperty(value = "创建人身份证号")
    private String createdBy;

    /**
     * 更新人身份证号
     */
    @TableField(value = "updated_by")
    @ApiModelProperty(value = "更新人身份证号")
    private String updatedBy;
}