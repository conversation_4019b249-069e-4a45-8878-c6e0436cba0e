package com.hl.archive.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 港澳台通行证表
 */
@ApiModel(description = "港澳台通行证表")
@Data
@TableName(value = "police_hk_macau_taiwan_permit")
public class PoliceHkMacauTaiwanPermit {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 身份证号
     */
    @TableField(value = "id_card")
    @ApiModelProperty(value = "身份证号")
    private String idCard;

    /**
     * 证件名称
     */
    @TableField(value = "document_name")
    @ApiModelProperty(value = "证件名称")
    private String documentName;

    /**
     * 证件号码
     */
    @TableField(value = "document_number")
    @ApiModelProperty(value = "证件号码")
    private String documentNumber;

    /**
     * 签发日期
     */
    @TableField(value = "issue_date")
    @ApiModelProperty(value = "签发日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date issueDate;

    /**
     * 有效期至
     */
    @TableField(value = "expiry_date")
    @ApiModelProperty(value = "有效期至")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expiryDate;

    /**
     * 保管机构
     */
    @TableField(value = "custody_organization")
    @ApiModelProperty(value = "保管机构")
    private String custodyOrganization;

    /**
     * 备注
     */
    @TableField(value = "remarks")
    @ApiModelProperty(value = "备注")
    private String remarks;

    /**
     * 逻辑删除(0:未删除,1:已删除)
     */
    @TableField(value = "is_deleted")
    @ApiModelProperty(value = "逻辑删除(0:未删除,1:已删除)")
    @TableLogic
    private Byte isDeleted;

    /**
     * 创建时间
     */
    @TableField(value = "created_at")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at")
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updatedAt;

    /**
     * 创建人身份证号
     */
    @TableField(value = "created_by")
    @ApiModelProperty(value = "创建人身份证号")
    private String createdBy;

    /**
     * 更新人身份证号
     */
    @TableField(value = "updated_by")
    @ApiModelProperty(value = "更新人身份证号")
    private String updatedBy;
}