package com.hl.archive.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 配偶子女移居国外情况表
 */
@ApiModel(description = "配偶子女移居国外情况表")
@Data
@TableName(value = "police_family_overseas_migration")
public class PoliceFamilyOverseasMigration {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 身份证号
     */
    @TableField(value = "id_card")
    @ApiModelProperty(value = "身份证号")
    private String idCard;

    /**
     * 配偶、子女姓名
     */
    @TableField(value = "family_member_name")
    @ApiModelProperty(value = "配偶、子女姓名")
    private String familyMemberName;

    /**
     * 移居国家（地区）
     */
    @TableField(value = "migration_country")
    @ApiModelProperty(value = "移居国家（地区）")
    private String migrationCountry;

    /**
     * 现居住城市
     */
    @TableField(value = "current_city")
    @ApiModelProperty(value = "现居住城市")
    private String currentCity;

    /**
     * 移居证件号码
     */
    @TableField(value = "migration_document_number")
    @ApiModelProperty(value = "移居证件号码")
    private String migrationDocumentNumber;

    /**
     * 移居类别
     */
    @TableField(value = "migration_category")
    @ApiModelProperty(value = "移居类别")
    private String migrationCategory;

    /**
     * 依据时间
     */
    @TableField(value = "basis_date")
    @ApiModelProperty(value = "依据时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date basisDate;

    /**
     * 备注
     */
    @TableField(value = "remarks")
    @ApiModelProperty(value = "备注")
    private String remarks;

    /**
     * 逻辑删除(0:未删除,1:已删除)
     */
    @TableField(value = "is_deleted")
    @ApiModelProperty(value = "逻辑删除(0:未删除,1:已删除)")
    @TableLogic
    private Byte isDeleted;

    /**
     * 创建时间
     */
    @TableField(value = "created_at")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at")
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updatedAt;

    /**
     * 创建人身份证号
     */
    @TableField(value = "created_by")
    @ApiModelProperty(value = "创建人身份证号")
    private String createdBy;

    /**
     * 更新人身份证号
     */
    @TableField(value = "updated_by")
    @ApiModelProperty(value = "更新人身份证号")
    private String updatedBy;
}