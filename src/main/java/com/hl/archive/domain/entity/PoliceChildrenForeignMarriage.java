package com.hl.archive.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 子女与外国人通婚情况表
 */
@ApiModel(description = "子女与外国人通婚情况表")
@Data
@TableName(value = "police_children_foreign_marriage")
public class PoliceChildrenForeignMarriage {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 身份证号
     */
    @TableField(value = "id_card")
    @ApiModelProperty(value = "身份证号")
    private String idCard;

    /**
     * 子女姓名
     */
    @TableField(value = "child_name")
    @ApiModelProperty(value = "子女姓名")
    private String childName;

    /**
     * 子女配偶姓名
     */
    @TableField(value = "spouse_name")
    @ApiModelProperty(value = "子女配偶姓名")
    private String spouseName;

    /**
     * 子女配偶国家
     */
    @TableField(value = "spouse_country")
    @ApiModelProperty(value = "子女配偶国家")
    private String spouseCountry;

    /**
     * 工作学习单位
     */
    @TableField(value = "work_study_unit")
    @ApiModelProperty(value = "工作学习单位")
    private String workStudyUnit;

    /**
     * 职务
     */
    @TableField(value = "`position`")
    @ApiModelProperty(value = "职务")
    private String position;

    /**
     * 登记时间
     */
    @TableField(value = "registration_date")
    @ApiModelProperty(value = "登记时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date registrationDate;

    /**
     * 逻辑删除(0:未删除,1:已删除)
     */
    @TableField(value = "is_deleted")
    @ApiModelProperty(value = "逻辑删除(0:未删除,1:已删除)")
    @TableLogic
    private Byte isDeleted;

    /**
     * 创建时间
     */
    @TableField(value = "created_at")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at")
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updatedAt;

    /**
     * 创建人身份证号
     */
    @TableField(value = "created_by")
    @ApiModelProperty(value = "创建人身份证号")
    private String createdBy;

    /**
     * 更新人身份证号
     */
    @TableField(value = "updated_by")
    @ApiModelProperty(value = "更新人身份证号")
    private String updatedBy;
}