package com.hl.archive.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * 警彩瞬间-素材上报
 */
@Data
@TableName(value = "police_archive.police_moment_submission")
public class PoliceMomentSubmission {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;


    /**
     * 报送单位
     */
    @TableField(value = "submit_unit")
    private String submitUnit;

    /**
     * 报送人
     */
    @TableField(value = "submitter")
    private String submitter;

    /**
     * 出境民警
     */
    @TableField(value = "officer_name")
    private String officerName;

    /**
     * 材料类型
     */
    @TableField(value = "material_type")
    private String materialType;

    /**
     * 报送类型
     */
    @TableField(value = "submission_type")
    private String submissionType;

    /**
     * 报送时间
     */
    @TableField(value = "submission_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date submissionTime;

    /**
     * 素材简介
     */
    @TableField(value = "material_intro")
    private String materialIntro;

    /**
     * 审核结果
     */
    @TableField(value = "audit_result")
    private String auditResult;

    /**
     * 创建时间
     */
    @TableField(value = "created_at")
    private Date createdAt;

    /**
     * 创建人
     */
    @TableField(value = "create_by")
    private String createBy;

    /**
     * 修改时间
     */
    @TableField(value = "updated_at")
    private Date updatedAt;

    /**
     * 修改人
     */
    @TableField(value = "update_by")
    private String updateBy;

    /**
     * 是否删除（0 否，1 是）
     */
    @TableField(value = "is_deleted")
    private Boolean isDeleted;

    @TableField(value = "zjbh")
    private String zjbh;

    @TableField(exist = false)
    private List<PoliceMomentSubmissionVideo> fileList;
}