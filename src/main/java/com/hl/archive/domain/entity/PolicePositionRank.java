package com.hl.archive.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 职务职级信息表
 */
@ApiModel(description = "职务职级信息表")
@Data
@TableName(value = "police_position_rank")
public class PolicePositionRank {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 身份证号
     */
    @TableField(value = "id_card")
    @ApiModelProperty(value = "身份证号")
    private String idCard;

    /**
     * 职务（职级）名称
     */
    @TableField(value = "position_name")
    @ApiModelProperty(value = "职务（职级）名称")
    private String positionName;

    /**
     * 公安职务级别
     */
    @TableField(value = "police_position_level")
    @ApiModelProperty(value = "公安职务级别")
    private String policePositionLevel;

    /**
     * 现职务时间
     */
    @TableField(value = "current_position_date")
    @ApiModelProperty(value = "现职务时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date currentPositionDate;

    /**
     * 现职级时间
     */
    @TableField(value = "current_rank_date")
    @ApiModelProperty(value = "现职级时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date currentRankDate;

    /**
     * 任职或试任职文号
     */
    @TableField(value = "appointment_document")
    @ApiModelProperty(value = "任职或试任职文号")
    private String appointmentDocument;

    /**
     * 逻辑删除(0:未删除,1:已删除)
     */
    @TableField(value = "is_deleted")
    @ApiModelProperty(value = "逻辑删除(0:未删除,1:已删除)")
    @TableLogic
    private Byte isDeleted;

    /**
     * 创建时间
     */
    @TableField(value = "created_at")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at")
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updatedAt;

    /**
     * 创建人身份证号
     */
    @TableField(value = "created_by")
    @ApiModelProperty(value = "创建人身份证号")
    private String createdBy;

    /**
     * 更新人身份证号
     */
    @TableField(value = "updated_by")
    @ApiModelProperty(value = "更新人身份证号")
    private String updatedBy;
}