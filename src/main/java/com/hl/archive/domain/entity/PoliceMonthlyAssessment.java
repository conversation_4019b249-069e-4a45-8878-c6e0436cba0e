package com.hl.archive.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Date;

import lombok.Data;

/**
 * 月度考核表
 */
@ApiModel(description = "月度考核表")
@Data
@TableName(value = "police_monthly_assessment")
public class PoliceMonthlyAssessment {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 身份证号
     */
    @TableField(value = "id_card")
    @ApiModelProperty(value = "身份证号")
    private String idCard;

    /**
     * 考评通报
     */
    @TableField(value = "assessment_notice")
    @ApiModelProperty(value = "考评通报")
    private String assessmentNotice;

    /**
     * 考评年度
     */
    @TableField(value = "assessment_year")
    @ApiModelProperty(value = "考评年度")
    private String assessmentYear;

    /**
     * 考评月份(1-12)
     */
    @TableField(value = "assessment_month")
    @ApiModelProperty(value = "考评月份(1-12)")
    private String assessmentMonth;

    /**
     * 考评得分
     */
    @TableField(value = "assessment_score")
    @ApiModelProperty(value = "考评得分")
    private String assessmentScore;

    /**
     * 考评奖金
     */
    @TableField(value = "assessment_bonus")
    @ApiModelProperty(value = "考评奖金")
    private String assessmentBonus;

    /**
     * 考评排名
     */
    @TableField(value = "assessment_ranking")
    @ApiModelProperty(value = "考评排名")
    private String assessmentRanking;

    /**
     * 逻辑删除(0:未删除,1:已删除)
     */
    @TableField(value = "is_deleted")
    @ApiModelProperty(value = "逻辑删除(0:未删除,1:已删除)")
    @TableLogic
    private Byte isDeleted;

    /**
     * 创建时间
     */
    @TableField(value = "created_at")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at")
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updatedAt;

    /**
     * 创建人身份证号
     */
    @TableField(value = "created_by")
    @ApiModelProperty(value = "创建人身份证号")
    private String createdBy;

    /**
     * 更新人身份证号
     */
    @TableField(value = "updated_by")
    @ApiModelProperty(value = "更新人身份证号")
    private String updatedBy;
}