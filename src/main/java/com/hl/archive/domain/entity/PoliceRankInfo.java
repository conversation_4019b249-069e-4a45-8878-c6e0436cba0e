package com.hl.archive.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 警衔信息表
 */
@ApiModel(description = "警衔信息表")
@Data
@TableName(value = "police_rank_info")
public class PoliceRankInfo {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 身份证号
     */
    @TableField(value = "id_card")
    @ApiModelProperty(value = "身份证号")
    private String idCard;

    /**
     * 衔称
     */
    @TableField(value = "rank_title")
    @ApiModelProperty(value = "衔称")
    private String rankTitle;

    /**
     * 授（晋）衔时间
     */
    @TableField(value = "promotion_date")
    @ApiModelProperty(value = "授（晋）衔时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date promotionDate;

    /**
     * 授晋衔原因
     */
    @TableField(value = "promotion_reason")
    @ApiModelProperty(value = "授晋衔原因")
    private String promotionReason;

    /**
     * 授衔种类
     */
    @TableField(value = "rank_type")
    @ApiModelProperty(value = "授衔种类")
    private String rankType;

    /**
     * 授衔时行政职级
     */
    @TableField(value = "admin_level_at_promotion")
    @ApiModelProperty(value = "授衔时行政职级")
    private String adminLevelAtPromotion;

    /**
     * 衔称终止日期
     */
    @TableField(value = "rank_end_date")
    @ApiModelProperty(value = "衔称终止日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date rankEndDate;

    /**
     * 衔称起算日期
     */
    @TableField(value = "rank_start_date")
    @ApiModelProperty(value = "衔称起算日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date rankStartDate;

    /**
     * 逻辑删除(0:未删除,1:已删除)
     */
    @TableField(value = "is_deleted")
    @ApiModelProperty(value = "逻辑删除(0:未删除,1:已删除)")
    @TableLogic
    private Byte isDeleted;

    /**
     * 创建时间
     */
    @TableField(value = "created_at")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at")
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updatedAt;

    /**
     * 创建人身份证号
     */
    @TableField(value = "created_by")
    @ApiModelProperty(value = "创建人身份证号")
    private String createdBy;

    /**
     * 更新人身份证号
     */
    @TableField(value = "updated_by")
    @ApiModelProperty(value = "更新人身份证号")
    private String updatedBy;
}