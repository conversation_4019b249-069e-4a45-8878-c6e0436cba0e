package com.hl.archive.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 民警请休假数据汇总
 */
@ApiModel(description="民警请休假数据汇总")
@Data
@TableName(value = "police_leave_record")
public class PoliceLeaveRecord {
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="")
    private Long id;

    /**
     * 开始时间
     */
    @TableField(value = "start_date")
    @ApiModelProperty(value="开始时间")
    private Date startDate;

    /**
     * 结束时间
     */
    @TableField(value = "end_date")
    @ApiModelProperty(value="结束时间")
    private Date endDate;

    /**
     * 关联身份证
     */
    @TableField(value = "id_card")
    @ApiModelProperty(value="关联身份证")
    private String idCard;

    /**
     * 类型
     */
    @TableField(value = "leave_type")
    @ApiModelProperty(value="类型")
    private String leaveType;

    @TableField(value = "is_deleted")
    @ApiModelProperty(value="")
    private Byte isDeleted;

    /**
     * 创建时间
     */
    @TableField(value = "create_at")
    @ApiModelProperty(value="创建时间")
    private Date createAt;

    /**
     * 更新时间
     */
    @TableField(value = "update_at")
    @ApiModelProperty(value="更新时间")
    private Date updateAt;
}