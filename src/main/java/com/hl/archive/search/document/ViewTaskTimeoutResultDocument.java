package com.hl.archive.search.document;

import lombok.Data;
import org.dromara.easyes.annotation.IndexField;
import org.dromara.easyes.annotation.IndexId;
import org.dromara.easyes.annotation.IndexName;

import java.util.Date;

@Data
@IndexName(value = "view_task_timeout_result")
public class ViewTaskTimeoutResultDocument {
    /**
     * 标号
     */
    @IndexId(value = "id")
    private String id;

    /**
     * 标题
     */
    @IndexField(value = "title")
    private String title;

    /**
     * 1红，2橙，3黄，4蓝
     */
    @IndexField(value = "`level`")
    private Integer level;

    /**
     * 1：一次性任务，2：需要多次完成
     */
    @IndexField(value = "`mode`")
    private Integer mode;

    /**
     * 内容
     */
    @IndexField(value = "content")
    private String content;

    /**
     * 人员
     */
    @IndexField(value = "persons")
    private String persons;

    /**
     * 任务开始时间
     */
    @IndexField(value = "work_starttime")
    private String workStarttime;

    /**
     * 任务结束时间
     */
    @IndexField(value = "work_endtime")
    private String workEndtime;

    /**
     * 项目相关
     */
    @IndexField(value = "project_id")
    private String projectId;

    /**
     * SSO中项目编号
     */
    @IndexField(value = "sso_project")
    private String ssoProject;

    /**
     * 创建人员（如果系统字段，为空）
     */
    @IndexField(value = "police_id")
    private String policeId;

    /**
     * 是否对附件可见，内容如下：{"invisible": ["_work"]}
     */
    @IndexField(value = "permission")
    private String permission;

    /**
     * 任务所属单位
     */
    @IndexField(value = "organization")
    private String organization;

    /**
     * 涉及的相关数据
     */
    @IndexField(value = "relation_data")
    private String relationData;

    /**
     * 附带信息
     */
    @IndexField(value = "annex_info")
    private String annexInfo;

    /**
     * 创建时间
     */
    @IndexField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @IndexField(value = "update_time")
    private Date updateTime;

    /**
     * 当前第几步骤
     */
    @IndexField(value = "person_level")
    private Integer personLevel;

    /**
     * 第几次循环工作
     */
    @IndexField(value = "result_level")
    private Integer resultLevel;

    @IndexField(value = "work_person")
    private String workPerson;

    @IndexField(value = "cowork_person")
    private String coworkPerson;

    @IndexField(value = "approve_person")
    private String approvePerson;

    /**
     * 当前状态， -1撤销， 0未开始，1未完成，10已完成
     */
    @IndexField(value = "`status`")
    private Integer status;

    /**
     * 0：未超时，1：已超时
     */
    @IndexField(value = "timeout")
    private Integer timeout;

    /**
     * 任务处理层级
     */
    @IndexField(value = "handle_level")
    private Integer handleLevel;

    /**
     * 流转时间
     */
    @IndexField(value = "last_time")
    private Date lastTime;

    /**
     * 业务子类型
     */
    @IndexField(value = "sub_business_type")
    private Integer subBusinessType;

    /**
     * 业务分类
     */
    @IndexField(value = "business_classify")
    private String businessClassify;

    /**
     * 父任务ID
     */
    @IndexField(value = "parent_id")
    private String parentId;

    @IndexField(value = "is_delete")
    private Integer isDelete;

    @IndexField(value = "result_id")
    private String resultId;

    /**
     * 操作类型，sign,work,approve
     */
    @IndexField(value = "`type`")
    private String type;

    /**
     * 时间
     */
    @IndexField(value = "`time`")
    private Date time;

    /**
     * 是否超时，1超时，0不超时
     */
    @IndexField(value = "time_out")
    private Integer timeOut;

    /**
     * 内容，数组格式
     */
    @IndexField(value = "`data`")
    private String data;

    @IndexField(value = "timeout_type")
    private String timeoutType;
}