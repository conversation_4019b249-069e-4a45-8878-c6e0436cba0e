package com.hl.archive.search.task;

import com.hl.archive.search.service.DataSyncService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 数据同步定时任务
 */
@Slf4j
@Component
@ConditionalOnProperty(name = "search.sync.enabled", havingValue = "true", matchIfMissing = false)
public class DataSyncTask {

    @Autowired
    private DataSyncService dataSyncService;

    /**
     * 每天凌晨2点执行全量同步
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void dailyFullSync() {
        log.info("开始执行每日全量同步任务");
        
        try {
            DataSyncService.SyncResult result = dataSyncService.syncAllData();
            
            if (result.isSuccess()) {
                log.info("每日全量同步完成，成功：{}，失败：{}，耗时：{}ms", 
                    result.getSuccessCount(), result.getFailCount(), result.getDuration());
            } else {
                log.error("每日全量同步失败：{}", result.getMessage());
            }
            
        } catch (Exception e) {
            log.error("每日全量同步任务执行异常", e);
        }
    }

    /**
     * 每小时执行增量同步（暂未实现增量同步逻辑）
     */
    @Scheduled(cron = "0 0 * * * ?")
    public void hourlyIncrementalSync() {
        log.info("开始执行每小时增量同步任务");
        
        try {
            // TODO: 实现增量同步逻辑
            // 可以根据数据表的更新时间字段来实现增量同步
            log.info("增量同步功能暂未实现");
            
        } catch (Exception e) {
            log.error("每小时增量同步任务执行异常", e);
        }
    }

    /**
     * 每周日凌晨3点重建索引
     */
    @Scheduled(cron = "0 0 3 ? * SUN")
    public void weeklyRebuildIndex() {
        log.info("开始执行每周重建索引任务");
        
        try {
            DataSyncService.SyncResult result = dataSyncService.rebuildIndex();
            
            if (result.isSuccess()) {
                log.info("每周重建索引完成，成功：{}，失败：{}，耗时：{}ms", 
                    result.getSuccessCount(), result.getFailCount(), result.getDuration());
            } else {
                log.error("每周重建索引失败：{}", result.getMessage());
            }
            
        } catch (Exception e) {
            log.error("每周重建索引任务执行异常", e);
        }
    }

    /**
     * 手动触发全量同步
     */
    public DataSyncService.SyncResult manualFullSync() {
        log.info("手动触发全量同步");
        return dataSyncService.syncAllData();
    }

    /**
     * 手动触发重建索引
     */
    public DataSyncService.SyncResult manualRebuildIndex() {
        log.info("手动触发重建索引");
        return dataSyncService.rebuildIndex();
    }
}
