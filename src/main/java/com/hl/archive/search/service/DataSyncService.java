package com.hl.archive.search.service;

/**
 * 数据同步服务接口
 * 负责将数据库中的数据同步到 Elasticsearch
 */
public interface DataSyncService {

    /**
     * 全量同步所有数据
     *
     * @return 同步结果
     */
    SyncResult syncAllData();

    /**
     * 同步指定数据类型的数据
     *
     * @param dataType 数据类型
     * @return 同步结果
     */
    SyncResult syncDataByType(String dataType);

    /**
     * 同步指定人员的所有数据
     *
     * @param idCard 身份证号
     * @return 同步结果
     */
    SyncResult syncDataByIdCard(String idCard);

    /**
     * 增量同步数据（同步指定时间之后的数据）
     *
     * @param lastSyncTime 上次同步时间（格式：yyyy-MM-dd HH:mm:ss）
     * @return 同步结果
     */
    SyncResult incrementalSync(String lastSyncTime);

    /**
     * 同步基本信息数据
     *
     * @return 同步结果
     */
    SyncResult syncBasicInfo();

    /**
     * 同步教育信息数据
     *
     * @return 同步结果
     */
    SyncResult syncEducation();

    /**
     * 同步培训信息数据
     *
     * @return 同步结果
     */
    SyncResult syncTraining();

    /**
     * 同步训历档案数据
     *
     * @return 同步结果
     */
    SyncResult syncTrainingRecords();

    /**
     * 同步履历信息数据
     *
     * @return 同步结果
     */
    SyncResult syncResume();

    /**
     * 同步职业能力标签数据
     *
     * @return 同步结果
     */
    SyncResult syncAbilityTag();

    /**
     * 同步警衔信息数据
     *
     * @return 同步结果
     */
    SyncResult syncRankInfo();

    /**
     * 同步家庭成员数据
     *
     * @return 同步结果
     */
    SyncResult syncFamilyMembers();

    /**
     * 同步健康状况数据
     *
     * @return 同步结果
     */
    SyncResult syncHealthStatus();

    /**
     * 同步荣誉信息数据
     *
     * @return 同步结果
     */
    SyncResult syncHonors();

    /**
     * 删除指定数据类型和记录ID的文档
     *
     * @param dataType 数据类型
     * @param recordId 记录ID
     * @return 是否成功
     */
    boolean deleteDocument(String dataType, Long recordId);

    /**
     * 删除指定身份证号的所有文档
     *
     * @param idCard 身份证号
     * @return 删除的文档数量
     */
    long deleteDocumentsByIdCard(String idCard);

    /**
     * 重建索引
     *
     * @return 同步结果
     */
    SyncResult rebuildIndex();

    /**
     * 获取同步状态
     *
     * @return 同步状态
     */
    SyncStatus getSyncStatus();

    /**
     * 同步结果
     */
    class SyncResult {
        private boolean success;
        private String message;
        private long totalCount;
        private long successCount;
        private long failCount;
        private long startTime;
        private long endTime;
        private long duration;

        public SyncResult() {
            this.startTime = System.currentTimeMillis();
        }

        public void finish() {
            this.endTime = System.currentTimeMillis();
            this.duration = this.endTime - this.startTime;
        }

        // Getters and Setters
        public boolean isSuccess() {
            return success;
        }

        public void setSuccess(boolean success) {
            this.success = success;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public long getTotalCount() {
            return totalCount;
        }

        public void setTotalCount(long totalCount) {
            this.totalCount = totalCount;
        }

        public long getSuccessCount() {
            return successCount;
        }

        public void setSuccessCount(long successCount) {
            this.successCount = successCount;
        }

        public long getFailCount() {
            return failCount;
        }

        public void setFailCount(long failCount) {
            this.failCount = failCount;
        }

        public long getStartTime() {
            return startTime;
        }

        public void setStartTime(long startTime) {
            this.startTime = startTime;
        }

        public long getEndTime() {
            return endTime;
        }

        public void setEndTime(long endTime) {
            this.endTime = endTime;
        }

        public long getDuration() {
            return duration;
        }

        public void setDuration(long duration) {
            this.duration = duration;
        }
    }

    /**
     * 同步状态
     */
    class SyncStatus {
        private boolean syncing;
        private String currentTask;
        private long progress;
        private long total;
        private String lastSyncTime;
        private String nextSyncTime;

        // Getters and Setters
        public boolean isSyncing() {
            return syncing;
        }

        public void setSyncing(boolean syncing) {
            this.syncing = syncing;
        }

        public String getCurrentTask() {
            return currentTask;
        }

        public void setCurrentTask(String currentTask) {
            this.currentTask = currentTask;
        }

        public long getProgress() {
            return progress;
        }

        public void setProgress(long progress) {
            this.progress = progress;
        }

        public long getTotal() {
            return total;
        }

        public void setTotal(long total) {
            this.total = total;
        }

        public String getLastSyncTime() {
            return lastSyncTime;
        }

        public void setLastSyncTime(String lastSyncTime) {
            this.lastSyncTime = lastSyncTime;
        }

        public String getNextSyncTime() {
            return nextSyncTime;
        }

        public void setNextSyncTime(String nextSyncTime) {
            this.nextSyncTime = nextSyncTime;
        }
    }
}
