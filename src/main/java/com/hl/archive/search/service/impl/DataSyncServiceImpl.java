package com.hl.archive.search.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hl.archive.domain.entity.*;
import com.hl.archive.mapper.*;
import com.hl.archive.mapper.PoliceAbilityTagMapper;
import com.hl.archive.search.document.PoliceSearchDocument;
import com.hl.archive.search.enums.DataTypeEnum;
import com.hl.archive.search.service.DataSyncService;
import com.hl.archive.search.service.PoliceSearchService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 数据同步服务实现类
 */
@Slf4j
@Service
public class DataSyncServiceImpl implements DataSyncService {

    @Autowired
    private PoliceSearchService policeSearchService;

    // 各种 Mapper
    @Autowired
    private PoliceBasicInfoMapper policeBasicInfoMapper;
    @Autowired
    private PoliceEducationMapper policeEducationMapper;
    @Autowired
    private PoliceTrainingMapper policeTrainingMapper;
    @Autowired
    private PoliceTrainingRecordsMapper policeTrainingRecordsMapper;
    @Autowired
    private PoliceResumeMapper policeResumeMapper;
    @Autowired
    private PoliceSpecialtiesMapper policeSpecialtiesMapper;
    @Autowired
    private PoliceHonorsMapper policeHonorsMapper;
    @Autowired
    private PoliceAbilityTagMapper policeAbilityTagMapper;

    // 同步状态
    private final AtomicBoolean syncing = new AtomicBoolean(false);
    private final AtomicLong currentProgress = new AtomicLong(0);
    private final AtomicLong totalProgress = new AtomicLong(0);
    private volatile String currentTask = "";
    private volatile String lastSyncTime = "";

    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Override
    public SyncResult syncAllData() {
        if (!syncing.compareAndSet(false, true)) {
            SyncResult result = new SyncResult();
            result.setSuccess(false);
            result.setMessage("同步正在进行中，请稍后再试");
            return result;
        }

        SyncResult result = new SyncResult();
        try {
            log.info("开始全量同步所有数据");
            currentTask = "全量同步";
            currentProgress.set(0);
            
            long totalSuccess = 0;
            long totalFail = 0;

            // 同步各种类型的数据
            SyncResult basicResult = syncBasicInfo();
            totalSuccess += basicResult.getSuccessCount();
            totalFail += basicResult.getFailCount();
//
//            SyncResult educationResult = syncEducation();
//            totalSuccess += educationResult.getSuccessCount();
//            totalFail += educationResult.getFailCount();
//
//            SyncResult trainingResult = syncTraining();
//            totalSuccess += trainingResult.getSuccessCount();
//            totalFail += trainingResult.getFailCount();
//
//            SyncResult trainingRecordsResult = syncTrainingRecords();
//            totalSuccess += trainingRecordsResult.getSuccessCount();
//            totalFail += trainingRecordsResult.getFailCount();

            SyncResult resumeResult = syncResume();
            totalSuccess += resumeResult.getSuccessCount();
            totalFail += resumeResult.getFailCount();

            SyncResult abilityTagResult = syncAbilityTag();
            totalSuccess += abilityTagResult.getSuccessCount();
            totalFail += abilityTagResult.getFailCount();

            // 设置结果
            result.setSuccess(totalFail == 0);
            result.setSuccessCount(totalSuccess);
            result.setFailCount(totalFail);
            result.setTotalCount(totalSuccess + totalFail);
            result.setMessage(String.format("全量同步完成，成功：%d，失败：%d", totalSuccess, totalFail));

            lastSyncTime = dateFormat.format(new Date());
            log.info("全量同步完成，成功：{}，失败：{}", totalSuccess, totalFail);

        } catch (Exception e) {
            log.error("全量同步失败", e);
            result.setSuccess(false);
            result.setMessage("全量同步失败：" + e.getMessage());
        } finally {
            syncing.set(false);
            currentTask = "";
            result.finish();
        }

        return result;
    }

    @Override
    public SyncResult syncDataByType(String dataType) {
        SyncResult result = new SyncResult();
        
        try {
            DataTypeEnum dataTypeEnum = DataTypeEnum.getByCode(dataType);
            if (dataTypeEnum == null) {
                result.setSuccess(false);
                result.setMessage("不支持的数据类型：" + dataType);
                return result;
            }

            log.info("开始同步数据类型：{}", dataTypeEnum.getName());

            switch (dataTypeEnum) {
                case BASIC_INFO:
                    return syncBasicInfo();
                case EDUCATION:
                    return syncEducation();
                case TRAINING:
                    return syncTraining();
                case TRAINING_RECORDS:
                    return syncTrainingRecords();
                case RESUME:
                    return syncResume();
                case SPECIALTIES:
                    return syncSpecialties();
                case ABILITY_TAG:
                    return syncAbilityTag();
                case HONORS:
                    return syncHonors();
                default:
                    result.setSuccess(false);
                    result.setMessage("暂不支持同步数据类型：" + dataTypeEnum.getName());
                    break;
            }

        } catch (Exception e) {
            log.error("同步数据类型失败，dataType：{}", dataType, e);
            result.setSuccess(false);
            result.setMessage("同步失败：" + e.getMessage());
        } finally {
            result.finish();
        }

        return result;
    }

    @Override
    public SyncResult syncDataByIdCard(String idCard) {
        SyncResult result = new SyncResult();
        
        try {
            log.info("开始同步身份证号为 {} 的数据", idCard);
            
            long totalSuccess = 0;
            long totalFail = 0;

            // 先删除该身份证号的所有文档
            long deletedCount = policeSearchService.deleteByIdCard(idCard);
            log.info("删除身份证号 {} 的旧文档 {} 条", idCard, deletedCount);

            // 同步基本信息
            PoliceBasicInfo basicInfo = policeBasicInfoMapper.selectOne(
                new LambdaQueryWrapper<PoliceBasicInfo>().eq(PoliceBasicInfo::getIdCard, idCard)
            );
            if (basicInfo != null) {
                if (syncBasicInfoRecord(basicInfo)) {
                    totalSuccess++;
                } else {
                    totalFail++;
                }
            }

            // 同步教育信息
            List<PoliceEducation> educationList = policeEducationMapper.selectList(
                new LambdaQueryWrapper<PoliceEducation>().eq(PoliceEducation::getIdCard, idCard)
            );
            for (PoliceEducation education : educationList) {
                if (syncEducationRecord(education, basicInfo)) {
                    totalSuccess++;
                } else {
                    totalFail++;
                }
            }

            // 同步培训信息
            List<PoliceTraining> trainingList = policeTrainingMapper.selectList(
                new LambdaQueryWrapper<PoliceTraining>().eq(PoliceTraining::getIdCard, idCard)
            );
            for (PoliceTraining training : trainingList) {
                if (syncTrainingRecord(training, basicInfo)) {
                    totalSuccess++;
                } else {
                    totalFail++;
                }
            }

            // 同步训历档案
            List<PoliceTrainingRecords> trainingRecordsList = policeTrainingRecordsMapper.selectList(
                new LambdaQueryWrapper<PoliceTrainingRecords>().eq(PoliceTrainingRecords::getIdCard, idCard)
            );
            for (PoliceTrainingRecords trainingRecord : trainingRecordsList) {
                if (syncTrainingRecordsRecord(trainingRecord, basicInfo)) {
                    totalSuccess++;
                } else {
                    totalFail++;
                }
            }

            // 设置结果
            result.setSuccess(totalFail == 0);
            result.setSuccessCount(totalSuccess);
            result.setFailCount(totalFail);
            result.setTotalCount(totalSuccess + totalFail);
            result.setMessage(String.format("同步身份证号 %s 的数据完成，成功：%d，失败：%d", idCard, totalSuccess, totalFail));

            log.info("同步身份证号 {} 的数据完成，成功：{}，失败：{}", idCard, totalSuccess, totalFail);

        } catch (Exception e) {
            log.error("同步身份证号数据失败，idCard：{}", idCard, e);
            result.setSuccess(false);
            result.setMessage("同步失败：" + e.getMessage());
        } finally {
            result.finish();
        }

        return result;
    }

    @Override
    public SyncResult incrementalSync(String lastSyncTime) {
        // TODO: 实现增量同步
        SyncResult result = new SyncResult();
        result.setSuccess(false);
        result.setMessage("增量同步功能暂未实现");
        result.finish();
        return result;
    }

    @Override
    public SyncResult syncBasicInfo() {
        SyncResult result = new SyncResult();
        
        try {
            log.info("开始同步基本信息数据");
            currentTask = "同步基本信息";
            
            List<PoliceBasicInfo> basicInfoList = policeBasicInfoMapper.selectList(null);
            result.setTotalCount(basicInfoList.size());
            
            long successCount = 0;
            long failCount = 0;
            
            for (PoliceBasicInfo basicInfo : basicInfoList) {
                if (syncBasicInfoRecord(basicInfo)) {
                    successCount++;
                } else {
                    failCount++;
                }
                currentProgress.incrementAndGet();
            }
            
            result.setSuccessCount(successCount);
            result.setFailCount(failCount);
            result.setSuccess(failCount == 0);
            result.setMessage(String.format("基本信息同步完成，成功：%d，失败：%d", successCount, failCount));
            
            log.info("基本信息同步完成，成功：{}，失败：{}", successCount, failCount);
            
        } catch (Exception e) {
            log.error("同步基本信息失败", e);
            result.setSuccess(false);
            result.setMessage("同步基本信息失败：" + e.getMessage());
        } finally {
            result.finish();
        }
        
        return result;
    }

    @Override
    public SyncResult syncEducation() {
        SyncResult result = new SyncResult();
        
        try {
            log.info("开始同步教育信息数据");
            currentTask = "同步教育信息";
            
            List<PoliceEducation> educationList = policeEducationMapper.selectList(null);
            result.setTotalCount(educationList.size());
            
            long successCount = 0;
            long failCount = 0;
            
            // 获取所有基本信息，用于关联
            Map<String, PoliceBasicInfo> basicInfoMap = getBasicInfoMap();
            
            for (PoliceEducation education : educationList) {
                PoliceBasicInfo basicInfo = basicInfoMap.get(education.getIdCard());
                if (syncEducationRecord(education, basicInfo)) {
                    successCount++;
                } else {
                    failCount++;
                }
                currentProgress.incrementAndGet();
            }
            
            result.setSuccessCount(successCount);
            result.setFailCount(failCount);
            result.setSuccess(failCount == 0);
            result.setMessage(String.format("教育信息同步完成，成功：%d，失败：%d", successCount, failCount));
            
            log.info("教育信息同步完成，成功：{}，失败：{}", successCount, failCount);
            
        } catch (Exception e) {
            log.error("同步教育信息失败", e);
            result.setSuccess(false);
            result.setMessage("同步教育信息失败：" + e.getMessage());
        } finally {
            result.finish();
        }
        
        return result;
    }

    @Override
    public SyncResult syncTraining() {
        SyncResult result = new SyncResult();

        try {
            log.info("开始同步培训信息数据");
            currentTask = "同步培训信息";

            List<PoliceTraining> trainingList = policeTrainingMapper.selectList(null);
            result.setTotalCount(trainingList.size());

            long successCount = 0;
            long failCount = 0;

            // 获取所有基本信息，用于关联
            Map<String, PoliceBasicInfo> basicInfoMap = getBasicInfoMap();

            for (PoliceTraining training : trainingList) {
                PoliceBasicInfo basicInfo = basicInfoMap.get(training.getIdCard());
                if (syncTrainingRecord(training, basicInfo)) {
                    successCount++;
                } else {
                    failCount++;
                }
                currentProgress.incrementAndGet();
            }

            result.setSuccessCount(successCount);
            result.setFailCount(failCount);
            result.setSuccess(failCount == 0);
            result.setMessage(String.format("培训信息同步完成，成功：%d，失败：%d", successCount, failCount));

            log.info("培训信息同步完成，成功：{}，失败：{}", successCount, failCount);

        } catch (Exception e) {
            log.error("同步培训信息失败", e);
            result.setSuccess(false);
            result.setMessage("同步培训信息失败：" + e.getMessage());
        } finally {
            result.finish();
        }

        return result;
    }

    @Override
    public SyncResult syncTrainingRecords() {
        SyncResult result = new SyncResult();

        try {
            log.info("开始同步训历档案数据");
            currentTask = "同步训历档案";

            List<PoliceTrainingRecords> trainingRecordsList = policeTrainingRecordsMapper.selectList(null);
            result.setTotalCount(trainingRecordsList.size());

            long successCount = 0;
            long failCount = 0;

            // 获取所有基本信息，用于关联
            Map<String, PoliceBasicInfo> basicInfoMap = getBasicInfoMap();

            for (PoliceTrainingRecords trainingRecord : trainingRecordsList) {
                PoliceBasicInfo basicInfo = basicInfoMap.get(trainingRecord.getIdCard());
                if (syncTrainingRecordsRecord(trainingRecord, basicInfo)) {
                    successCount++;
                } else {
                    failCount++;
                }
                currentProgress.incrementAndGet();
            }

            result.setSuccessCount(successCount);
            result.setFailCount(failCount);
            result.setSuccess(failCount == 0);
            result.setMessage(String.format("训历档案同步完成，成功：%d，失败：%d", successCount, failCount));

            log.info("训历档案同步完成，成功：{}，失败：{}", successCount, failCount);

        } catch (Exception e) {
            log.error("同步训历档案失败", e);
            result.setSuccess(false);
            result.setMessage("同步训历档案失败：" + e.getMessage());
        } finally {
            result.finish();
        }

        return result;
    }

    @Override
    public SyncResult syncResume() {
        SyncResult result = new SyncResult();

        try {
            log.info("开始同步履历信息数据");
            currentTask = "同步履历信息";

            List<PoliceResume> resumeList = policeResumeMapper.selectList(null);
            result.setTotalCount(resumeList.size());

            long successCount = 0;
            long failCount = 0;

            // 获取所有基本信息，用于关联
            Map<String, PoliceBasicInfo> basicInfoMap = getBasicInfoMap();

            for (PoliceResume resume : resumeList) {
                PoliceBasicInfo basicInfo = basicInfoMap.get(resume.getIdCard());
                if (syncResumeRecord(resume, basicInfo)) {
                    successCount++;
                } else {
                    failCount++;
                }
                currentProgress.incrementAndGet();
            }

            result.setSuccessCount(successCount);
            result.setFailCount(failCount);
            result.setSuccess(failCount == 0);
            result.setMessage(String.format("履历信息同步完成，成功：%d，失败：%d", successCount, failCount));

            log.info("履历信息同步完成，成功：{}，失败：{}", successCount, failCount);

        } catch (Exception e) {
            log.error("同步履历信息失败", e);
            result.setSuccess(false);
            result.setMessage("同步履历信息失败：" + e.getMessage());
        } finally {
            result.finish();
        }

        return result;
    }

    @Override
    public SyncResult syncRankInfo() {
        // TODO: 实现警衔信息同步
        SyncResult result = new SyncResult();
        result.setSuccess(false);
        result.setMessage("警衔信息同步功能暂未实现");
        result.finish();
        return result;
    }

    @Override
    public SyncResult syncFamilyMembers() {
        // TODO: 实现家庭成员同步
        SyncResult result = new SyncResult();
        result.setSuccess(false);
        result.setMessage("家庭成员同步功能暂未实现");
        result.finish();
        return result;
    }

    @Override
    public SyncResult syncHealthStatus() {
        // TODO: 实现健康状况同步
        SyncResult result = new SyncResult();
        result.setSuccess(false);
        result.setMessage("健康状况同步功能暂未实现");
        result.finish();
        return result;
    }

    @Override
    public SyncResult syncHonors() {
        SyncResult result = new SyncResult();

        try {
            log.info("开始同步荣誉信息数据");
            currentTask = "同步荣誉信息";

            List<PoliceHonors> honorsList = policeHonorsMapper.selectList(null);
            result.setTotalCount(honorsList.size());

            long successCount = 0;
            long failCount = 0;

            // 获取所有基本信息，用于关联
            Map<String, PoliceBasicInfo> basicInfoMap = getBasicInfoMap();

            for (PoliceHonors honors : honorsList) {
                PoliceBasicInfo basicInfo = basicInfoMap.get(honors.getIdCard());
                if (syncHonorsRecord(honors, basicInfo)) {
                    successCount++;
                } else {
                    failCount++;
                }
                currentProgress.incrementAndGet();
            }

            result.setSuccessCount(successCount);
            result.setFailCount(failCount);
            result.setSuccess(failCount == 0);
            result.setMessage(String.format("荣誉信息同步完成，成功：%d，失败：%d", successCount, failCount));

            log.info("荣誉信息同步完成，成功：{}，失败：{}", successCount, failCount);

        } catch (Exception e) {
            log.error("同步荣誉信息失败", e);
            result.setSuccess(false);
            result.setMessage("同步荣誉信息失败：" + e.getMessage());
        } finally {
            result.finish();
        }

        return result;
    }

    public SyncResult syncSpecialties() {
        SyncResult result = new SyncResult();

        try {
            log.info("开始同步特长信息数据");
            currentTask = "同步特长信息";

            List<PoliceSpecialties> specialtiesList = policeSpecialtiesMapper.selectList(null);
            result.setTotalCount(specialtiesList.size());

            long successCount = 0;
            long failCount = 0;

            // 获取所有基本信息，用于关联
            Map<String, PoliceBasicInfo> basicInfoMap = getBasicInfoMap();

            for (PoliceSpecialties specialties : specialtiesList) {
                PoliceBasicInfo basicInfo = basicInfoMap.get(specialties.getIdCard());
                if (syncSpecialtiesRecord(specialties, basicInfo)) {
                    successCount++;
                } else {
                    failCount++;
                }
                currentProgress.incrementAndGet();
            }

            result.setSuccessCount(successCount);
            result.setFailCount(failCount);
            result.setSuccess(failCount == 0);
            result.setMessage(String.format("特长信息同步完成，成功：%d，失败：%d", successCount, failCount));

            log.info("特长信息同步完成，成功：{}，失败：{}", successCount, failCount);

        } catch (Exception e) {
            log.error("同步特长信息失败", e);
            result.setSuccess(false);
            result.setMessage("同步特长信息失败：" + e.getMessage());
        } finally {
            result.finish();
        }

        return result;
    }

    public SyncResult syncAbilityTag() {


       return null;
    }

    @Override
    public boolean deleteDocument(String dataType, Long recordId) {
        return policeSearchService.deleteByDataTypeAndRecordId(dataType, recordId);
    }

    @Override
    public long deleteDocumentsByIdCard(String idCard) {
        return policeSearchService.deleteByIdCard(idCard);
    }

    @Override
    public SyncResult rebuildIndex() {
        SyncResult result = new SyncResult();

        try {
            log.info("开始重建索引");

            // 删除现有索引
            if (policeSearchService.existsIndex()) {
                policeSearchService.deleteIndex();
                log.info("删除现有索引成功");
            }

            // 创建新索引
            if (policeSearchService.createIndex()) {
                log.info("创建新索引成功");

                // 重新同步所有数据
                return syncAllData();
            } else {
                result.setSuccess(false);
                result.setMessage("创建索引失败");
            }

        } catch (Exception e) {
            log.error("重建索引失败", e);
            result.setSuccess(false);
            result.setMessage("重建索引失败：" + e.getMessage());
        } finally {
            result.finish();
        }

        return result;
    }

    @Override
    public SyncStatus getSyncStatus() {
        SyncStatus status = new SyncStatus();
        status.setSyncing(syncing.get());
        status.setCurrentTask(currentTask);
        status.setProgress(currentProgress.get());
        status.setTotal(totalProgress.get());
        status.setLastSyncTime(lastSyncTime);
        // TODO: 设置下次同步时间
        status.setNextSyncTime("");
        return status;
    }

    /**
     * 获取所有基本信息的映射
     */
    private Map<String, PoliceBasicInfo> getBasicInfoMap() {
        List<PoliceBasicInfo> basicInfoList = policeBasicInfoMapper.selectList(null);
        Map<String, PoliceBasicInfo> basicInfoMap = new HashMap<>();
        for (PoliceBasicInfo basicInfo : basicInfoList) {
            basicInfoMap.put(basicInfo.getIdCard(), basicInfo);
        }
        return basicInfoMap;
    }

    /**
     * 同步基本信息记录
     */
    private boolean syncBasicInfoRecord(PoliceBasicInfo basicInfo) {
        try {
            PoliceSearchDocument document = new PoliceSearchDocument();

            // 设置文档ID
            document.setId(DataTypeEnum.BASIC_INFO.getCode() + "_" + basicInfo.getId());
            document.setDataType(DataTypeEnum.BASIC_INFO.getCode());
            document.setDataTypeName(DataTypeEnum.BASIC_INFO.getName());
            document.setRecordId(basicInfo.getId());
            document.setIdCard(basicInfo.getIdCard());

            // 设置人员基本信息
            document.setName(basicInfo.getName());

            // 设置搜索内容
            StringBuilder mainContent = new StringBuilder();
            mainContent.append(basicInfo.getName()).append(" ");
            if (basicInfo.getPositionName() != null) {
                mainContent.append(basicInfo.getPositionName()).append(" ");
            }
            if (basicInfo.getEducationLevel() != null) {
                mainContent.append(basicInfo.getEducationLevel()).append(" ");
            }
            if (basicInfo.getPoliticalIdentity() != null) {
                mainContent.append(basicInfo.getPoliticalIdentity()).append(" ");
            }
            document.setMainContent(mainContent.toString().trim());

            StringBuilder subContent = new StringBuilder();
            if (basicInfo.getHomeAddress() != null) {
                subContent.append(basicInfo.getHomeAddress()).append(" ");
            }
            if (basicInfo.getMobilePhone() != null) {
                subContent.append(basicInfo.getMobilePhone()).append(" ");
            }
            document.setSubContent(subContent.toString().trim());

            // 设置原始数据
            Map<String, Object> originalData = new HashMap<>();
            originalData.put("basicInfo", basicInfo);
            document.setOriginalData(originalData);

            // 设置重要程度

            return policeSearchService.save(document);

        } catch (Exception e) {
            log.error("同步基本信息记录失败，ID：{}", basicInfo.getId(), e);
            return false;
        }
    }

    /**
     * 同步教育信息记录
     */
    private boolean syncEducationRecord(PoliceEducation education, PoliceBasicInfo basicInfo) {
        try {
            PoliceSearchDocument document = new PoliceSearchDocument();

            // 设置文档ID
            document.setId(DataTypeEnum.EDUCATION.getCode() + "_" + education.getId());
            document.setDataType(DataTypeEnum.EDUCATION.getCode());
            document.setDataTypeName(DataTypeEnum.EDUCATION.getName());
            document.setRecordId(education.getId());
            document.setIdCard(education.getIdCard());

            // 设置人员基本信息
            if (basicInfo != null) {
                document.setName(basicInfo.getName());



            }

            // 设置搜索内容
            StringBuilder mainContent = new StringBuilder();
            if (education.getSchoolName() != null) {
                mainContent.append(education.getSchoolName()).append(" ");
            }
            if (education.getMajorName() != null) {
                mainContent.append(education.getMajorName()).append(" ");
            }
            if (education.getEducationLevel() != null) {
                mainContent.append(education.getEducationLevel()).append(" ");
            }
            if (education.getDegree() != null) {
                mainContent.append(education.getDegree()).append(" ");
            }
            document.setMainContent(mainContent.toString().trim());

            StringBuilder subContent = new StringBuilder();
            if (education.getEnrollmentDate() != null) {
                subContent.append("入学时间：").append(dateFormat.format(education.getEnrollmentDate())).append(" ");
            }
            if (education.getGraduationDate() != null) {
                subContent.append("毕业时间：").append(dateFormat.format(education.getGraduationDate())).append(" ");
            }
            document.setSubContent(subContent.toString().trim());



            // 设置时间信息
            if (education.getGraduationDate() != null) {
                Calendar cal = Calendar.getInstance();
                cal.setTime(education.getGraduationDate());

            }

            // 设置原始数据
            Map<String, Object> originalData = new HashMap<>();
            originalData.put("education", education);
            if (basicInfo != null) {
                originalData.put("basicInfo", basicInfo);
            }
            document.setOriginalData(originalData);



            return policeSearchService.save(document);

        } catch (Exception e) {
            log.error("同步教育信息记录失败，ID：{}", education.getId(), e);
            return false;
        }
    }

    /**
     * 同步培训信息记录
     */
    private boolean syncTrainingRecord(PoliceTraining training, PoliceBasicInfo basicInfo) {
        try {
            PoliceSearchDocument document = new PoliceSearchDocument();

            // 设置文档ID
            document.setId(DataTypeEnum.TRAINING.getCode() + "_" + training.getId());
            document.setDataType(DataTypeEnum.TRAINING.getCode());
            document.setDataTypeName(DataTypeEnum.TRAINING.getName());
            document.setRecordId(training.getId());
            document.setIdCard(training.getIdCard());

            // 设置人员基本信息
            if (basicInfo != null) {
                document.setName(basicInfo.getName());


            }

            // 设置搜索内容
            StringBuilder mainContent = new StringBuilder();
            if (training.getTrainingName() != null) {
                mainContent.append(training.getTrainingName()).append(" ");
            }
            if (training.getOrganizerName() != null) {
                mainContent.append(training.getOrganizerName()).append(" ");
            }
            document.setMainContent(mainContent.toString().trim());

            StringBuilder subContent = new StringBuilder();
            if (training.getTrainingStartDate() != null) {
                subContent.append("开始时间：").append(dateFormat.format(training.getTrainingStartDate())).append(" ");
            }
            if (training.getTrainingEndDate() != null) {
                subContent.append("结束时间：").append(dateFormat.format(training.getTrainingEndDate())).append(" ");
            }
            document.setSubContent(subContent.toString().trim());




            // 设置时间信息
            if (training.getTrainingStartDate() != null) {
                Calendar cal = Calendar.getInstance();
                cal.setTime(training.getTrainingStartDate());
            }

            // 设置原始数据
            Map<String, Object> originalData = new HashMap<>();
            originalData.put("training", training);
            if (basicInfo != null) {
                originalData.put("basicInfo", basicInfo);
            }
            document.setOriginalData(originalData);


            return policeSearchService.save(document);

        } catch (Exception e) {
            log.error("同步培训信息记录失败，ID：{}", training.getId(), e);
            return false;
        }
    }

    /**
     * 同步训历档案记录
     */
    private boolean syncTrainingRecordsRecord(PoliceTrainingRecords trainingRecord, PoliceBasicInfo basicInfo) {
        try {
            PoliceSearchDocument document = new PoliceSearchDocument();

            // 设置文档ID
            document.setId(DataTypeEnum.TRAINING_RECORDS.getCode() + "_" + trainingRecord.getId());
            document.setDataType(DataTypeEnum.TRAINING_RECORDS.getCode());
            document.setDataTypeName(DataTypeEnum.TRAINING_RECORDS.getName());
            document.setRecordId(trainingRecord.getId());
            document.setIdCard(trainingRecord.getIdCard());

            // 设置人员基本信息
            if (basicInfo != null) {
                document.setName(basicInfo.getName());
            }

            // 设置搜索内容
            StringBuilder mainContent = new StringBuilder();
            if (trainingRecord.getTrainingName() != null) {
                mainContent.append(trainingRecord.getTrainingName()).append(" ");
            }
            if (trainingRecord.getExamProjectName() != null) {
                mainContent.append(trainingRecord.getExamProjectName()).append(" ");
            }
            if (trainingRecord.getGrade() != null) {
                mainContent.append("成绩：").append(trainingRecord.getGrade()).append(" ");
            }
            document.setMainContent(mainContent.toString().trim());

            StringBuilder subContent = new StringBuilder();
            if (trainingRecord.getScore() != null) {
                subContent.append("分数：").append(trainingRecord.getScore()).append(" ");
            }
            if (trainingRecord.getTrainingTime() != null) {
                subContent.append("培训时间：").append(trainingRecord.getTrainingTime()).append(" ");
            }
            document.setSubContent(subContent.toString().trim());


            // 设置原始数据
            Map<String, Object> originalData = new HashMap<>();
            originalData.put("trainingRecord", trainingRecord);
            if (basicInfo != null) {
                originalData.put("basicInfo", basicInfo);
            }
            document.setOriginalData(originalData);



            return policeSearchService.save(document);

        } catch (Exception e) {
            log.error("同步训历档案记录失败，ID：{}", trainingRecord.getId(), e);
            return false;
        }
    }

    /**
     * 同步履历信息记录
     */
    private boolean syncResumeRecord(PoliceResume resume, PoliceBasicInfo basicInfo) {
        try {
            PoliceSearchDocument document = new PoliceSearchDocument();

            // 设置文档ID
            document.setId(DataTypeEnum.RESUME.getCode() + "_" + resume.getId());
            document.setDataType(DataTypeEnum.RESUME.getCode());
            document.setDataTypeName(DataTypeEnum.RESUME.getName());
            document.setRecordId(resume.getId());
            document.setIdCard(resume.getIdCard());

            // 设置人员基本信息
            if (basicInfo != null) {
                document.setName(basicInfo.getName());
            }

            // 设置搜索内容
            StringBuilder mainContent = new StringBuilder();
            if (resume.getWorkUnit() != null) {
                mainContent.append(resume.getWorkUnit()).append(" ");
            }
            if (resume.getPosition() != null) {
                mainContent.append(resume.getPosition()).append(" ");
            }
            document.setMainContent(mainContent.toString().trim());

            StringBuilder subContent = new StringBuilder();
            if (resume.getStartDate() != null) {
                subContent.append("开始时间：").append(dateFormat.format(resume.getStartDate())).append(" ");
            }
            if (resume.getEndDate() != null) {
                subContent.append("结束时间：").append(dateFormat.format(resume.getEndDate())).append(" ");
            }
            document.setSubContent(subContent.toString().trim());

            Map<String, Object> originalData = new HashMap<>();
            originalData.put("resume", resume);
            document.setOriginalData(originalData);
            return policeSearchService.save(document);
        } catch (Exception e) {
            log.error("同步履历信息记录失败，ID：{}", resume.getId(), e);
            return false;
        }
    }

    /**
     * 同步荣誉信息记录
     */
    private boolean syncHonorsRecord(PoliceHonors honors, PoliceBasicInfo basicInfo) {
        try {
            PoliceSearchDocument document = new PoliceSearchDocument();

            // 设置文档ID
            document.setId(DataTypeEnum.HONORS.getCode() + "_" + honors.getId());
            document.setDataType(DataTypeEnum.HONORS.getCode());
            document.setDataTypeName(DataTypeEnum.HONORS.getName());
            document.setRecordId(honors.getId());
            document.setIdCard(honors.getIdCard());

            // 设置人员基本信息
            if (basicInfo != null) {
                document.setName(basicInfo.getName());


            }

            // 设置搜索内容
            StringBuilder mainContent = new StringBuilder();
            if (honors.getHonorName() != null) {
                mainContent.append(honors.getHonorName()).append(" ");
            }
            if (honors.getApproveAuthority() != null) {
                mainContent.append(honors.getApproveAuthority()).append(" ");
            }
            document.setMainContent(mainContent.toString().trim());

            StringBuilder subContent = new StringBuilder();
            if (honors.getAwardDate() != null) {
                subContent.append("获奖时间：").append(dateFormat.format(honors.getAwardDate())).append(" ");
            }
            document.setSubContent(subContent.toString().trim());


            if (honors.getAwardDate() != null) {
                Calendar cal = Calendar.getInstance();
                cal.setTime(honors.getAwardDate());

            }

            // 设置原始数据
            Map<String, Object> originalData = new HashMap<>();
            originalData.put("honors", honors);
            if (basicInfo != null) {
                originalData.put("basicInfo", basicInfo);
            }
            document.setOriginalData(originalData);



            return policeSearchService.save(document);

        } catch (Exception e) {
            log.error("同步荣誉信息记录失败，ID：{}", honors.getId(), e);
            return false;
        }
    }

    /**
     * 同步特长信息记录
     */
    private boolean syncSpecialtiesRecord(PoliceSpecialties specialties, PoliceBasicInfo basicInfo) {
        try {
            PoliceSearchDocument document = new PoliceSearchDocument();

            // 设置文档ID
            document.setId(DataTypeEnum.SPECIALTIES.getCode() + "_" + specialties.getId());
            document.setDataType(DataTypeEnum.SPECIALTIES.getCode());
            document.setDataTypeName(DataTypeEnum.SPECIALTIES.getName());
            document.setRecordId(specialties.getId());
            document.setIdCard(specialties.getIdCard());

            // 设置人员基本信息
            if (basicInfo != null) {
                document.setName(basicInfo.getName());
            }

            // 设置搜索内容
            StringBuilder mainContent = new StringBuilder();
            if (specialties.getSpecialtyName() != null) {
                mainContent.append(specialties.getSpecialtyName()).append(" ");
            }
            if (specialties.getApproveAuthority() != null) {
                mainContent.append(specialties.getApproveAuthority()).append(" ");
            }
            document.setMainContent(mainContent.toString().trim());

            StringBuilder subContent = new StringBuilder();
            if (specialties.getAwardDate() != null) {
                subContent.append("获奖时间：").append(dateFormat.format(specialties.getAwardDate())).append(" ");
            }
            document.setSubContent(subContent.toString().trim());
            if (specialties.getAwardDate() != null) {
                Calendar cal = Calendar.getInstance();
                cal.setTime(specialties.getAwardDate());

            }

            // 设置原始数据
            Map<String, Object> originalData = new HashMap<>();
            originalData.put("specialties", specialties);
            document.setOriginalData(originalData);



            return policeSearchService.save(document);

        } catch (Exception e) {
            log.error("同步特长信息记录失败，ID：{}", specialties.getId(), e);
            return false;
        }
    }

    /**
     * 同步职业能力标签记录
     */
    private boolean syncAbilityTagRecord(PoliceAbilityTag abilityTag, PoliceBasicInfo basicInfo) {
       return true;
    }
}
