package com.hl.archive.search.service.impl;


import com.hl.archive.search.document.PoliceSearchDocument;
import com.hl.archive.search.dto.SearchRequest;
import com.hl.archive.search.mapper.PoliceSearchMapper;
import com.hl.archive.search.service.PoliceSearchService;
import lombok.extern.slf4j.Slf4j;
import org.dromara.easyes.core.biz.EsPageInfo;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;

/**
 * 民警搜索服务实现类 - 兼容版本
 * 使用基础的 Easy-ES 功能，确保与 ES 6.x 兼容
 */
@Slf4j
@Service
public class PoliceSearchServiceImpl implements PoliceSearchService {

    @Autowired
    private PoliceSearchMapper policeSearchMapper;

    @Override
    public EsPageInfo<PoliceSearchDocument> search(SearchRequest request) {
        long startTime = System.currentTimeMillis();
        log.info("开始搜索，关键词：{}", request.getKeyword());

        try {
            // 构建基础查询条件
            LambdaEsQueryWrapper<PoliceSearchDocument> wrapper = new LambdaEsQueryWrapper<>();

//            // 基础过滤条件
//            wrapper.eq(PoliceSearchDocument::getStatus, 1);

            // 关键词搜索
            if (StringUtils.hasText(request.getKeyword())) {
                wrapper.match(PoliceSearchDocument::getName, request.getKeyword())
                       .or()
                       .match(PoliceSearchDocument::getMainContent, request.getKeyword())
                       .or()
                       .match(PoliceSearchDocument::getSubContent, request.getKeyword());
            }

            // 数据类型过滤
            if (!CollectionUtils.isEmpty(request.getDataTypes())) {
                wrapper.in(PoliceSearchDocument::getDataType, request.getDataTypes());
            }

            // 设置排序
//            wrapper.orderByDesc(PoliceSearchDocument::getId);

            // 执行搜索
            EsPageInfo<PoliceSearchDocument> pageInfo = policeSearchMapper.pageQuery(wrapper, request.getPage(), request.getLimit());

            return pageInfo;
        } catch (Exception e) {
            log.error("搜索失败", e);
            throw new RuntimeException("搜索失败：" + e.getMessage());
        }
    }

    @Override
    public PoliceSearchDocument getById(String id) {
        try {
            return policeSearchMapper.selectById(id);
        } catch (Exception e) {
            log.error("根据ID获取文档失败，ID：{}", id, e);
            return null;
        }
    }

    @Override
    public boolean save(PoliceSearchDocument document) {
        try {
            document.setCreateTime(new Date());
            document.setUpdateTime(new Date());
            document.setStatus(1);
            
            Integer result = policeSearchMapper.insert(document);
            return result != null && result > 0;
        } catch (Exception e) {
            log.error("保存文档失败", e);
            return false;
        }
    }

    @Override
    public boolean saveBatch(List<PoliceSearchDocument> documents) {
        if (CollectionUtils.isEmpty(documents)) {
            return true;
        }
        
        try {
            Date now = new Date();
            documents.forEach(doc -> {
                doc.setCreateTime(now);
                doc.setUpdateTime(now);
                doc.setStatus(1);
            });
            
            Integer result = policeSearchMapper.insertBatch(documents);
            return result != null && result > 0;
        } catch (Exception e) {
            log.error("批量保存文档失败", e);
            return false;
        }
    }

    @Override
    public boolean deleteById(String id) {
        try {
            Integer result = policeSearchMapper.deleteById(id);
            return result != null && result > 0;
        } catch (Exception e) {
            log.error("删除文档失败，ID：{}", id, e);
            return false;
        }
    }

    @Override
    public boolean deleteByDataTypeAndRecordId(String dataType, Long recordId) {
        try {
            String id = dataType + "_" + recordId;
            return deleteById(id);
        } catch (Exception e) {
            log.error("根据数据类型和记录ID删除文档失败，dataType：{}，recordId：{}", dataType, recordId, e);
            return false;
        }
    }

    @Override
    public long deleteByIdCard(String idCard) {
        try {
            LambdaEsQueryWrapper<PoliceSearchDocument> wrapper = new LambdaEsQueryWrapper<>();
            wrapper.eq(PoliceSearchDocument::getIdCard, idCard);
            
            Integer result = policeSearchMapper.delete(wrapper);
            return result != null ? result : 0;
        } catch (Exception e) {
            log.error("根据身份证号删除文档失败，idCard：{}", idCard, e);
            return 0;
        }
    }

    @Override
    public boolean update(PoliceSearchDocument document) {
        try {
            document.setUpdateTime(new Date());
            Integer result = policeSearchMapper.updateById(document);
            return result != null && result > 0;
        } catch (Exception e) {
            log.error("更新文档失败", e);
            return false;
        }
    }

    @Override
    public boolean createIndex() {
        try {
            return policeSearchMapper.createIndex();
        } catch (Exception e) {
            log.error("创建索引失败", e);
            return false;
        }
    }

    @Override
    public boolean deleteIndex() {
        try {
            return policeSearchMapper.deleteIndex();
        } catch (Exception e) {
            log.error("删除索引失败", e);
            return false;
        }
    }

    @Override
    public boolean existsIndex() {
        try {
            return policeSearchMapper.existsIndex("police_search_index");
        } catch (Exception e) {
            log.error("检查索引是否存在失败", e);
            return false;
        }
    }





}
