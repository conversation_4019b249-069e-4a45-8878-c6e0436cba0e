package com.hl.archive.service;

import cn.hutool.core.util.StrUtil;
import cn.idev.excel.FastExcel;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.dto.PolicePersonWarnRecordRequestDTO;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hl.archive.domain.entity.PolicePersonWarnRecord;
import com.hl.archive.mapper.PolicePersonWarnRecordMapper;

import javax.servlet.http.HttpServletResponse;

@Service
public class PolicePersonWarnRecordService extends ServiceImpl<PolicePersonWarnRecordMapper, PolicePersonWarnRecord> {

    public Page<PolicePersonWarnRecord> pageList(PolicePersonWarnRecordRequestDTO requestDTO) {

        if (StrUtil.isNotBlank(requestDTO.getOrganizationId())) {
            if (!"320412000000".equals(requestDTO.getOrganizationId())) {
                // 非320412000000时，截取前8位
                requestDTO.setOrganizationId(requestDTO.getOrganizationId().substring(0, 8));
            } else {
                requestDTO.setOrganizationId(null);
            }
        }
        Page<PolicePersonWarnRecord> page = this.page(Page.of(requestDTO.getPage(), requestDTO.getLimit()), Wrappers.<PolicePersonWarnRecord>lambdaQuery()
                .like(StrUtil.isNotBlank(requestDTO.getIdCard()), PolicePersonWarnRecord::getIdCard, requestDTO.getIdCard())
                .eq(StrUtil.isNotBlank(requestDTO.getWarnType()), PolicePersonWarnRecord::getWarnType, requestDTO.getWarnType())
                .like(StrUtil.isNotBlank(requestDTO.getName()), PolicePersonWarnRecord::getName, requestDTO.getName())
                .likeRight(StrUtil.isNotBlank(requestDTO.getOrganizationId()), PolicePersonWarnRecord::getOrganizationId, requestDTO.getOrganizationId())
                .ge(requestDTO.getWarnStartTime() != null, PolicePersonWarnRecord::getWarnTime, requestDTO.getWarnStartTime())
                .le(requestDTO.getWarnEndTime() != null, PolicePersonWarnRecord::getWarnTime, requestDTO.getWarnEndTime())
                .and(StrUtil.isNotBlank(requestDTO.getQuery()), w ->
                        w.like(StrUtil.isNotBlank(requestDTO.getQuery()), PolicePersonWarnRecord::getName, requestDTO.getQuery())
                                .or()
                                .like(StrUtil.isNotBlank(requestDTO.getQuery()), PolicePersonWarnRecord::getIdCard, requestDTO.getQuery())
                                .or()
                                .like(StrUtil.isNotBlank(requestDTO.getQuery()), PolicePersonWarnRecord::getPoliceNumber, requestDTO.getQuery())
                                .or()
                                .like(StrUtil.isNotBlank(requestDTO.getQuery()), PolicePersonWarnRecord::getDescription, requestDTO.getQuery())
                                .or()
                                .like(StrUtil.isNotBlank(requestDTO.getQuery()), PolicePersonWarnRecord::getDataKey, requestDTO.getQuery())
                )
                .orderByDesc(PolicePersonWarnRecord::getWarnTime));
        return page;
    }

    public void exportWarnInfo(PolicePersonWarnRecordRequestDTO requestDTO, HttpServletResponse response) throws IOException {
        requestDTO.setLimit(Integer.MAX_VALUE);
        Page<PolicePersonWarnRecord> policePersonWarnRecordPage = this.pageList(requestDTO);
        List<PolicePersonWarnRecord> records = policePersonWarnRecordPage.getRecords();
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        FastExcel.write(response.getOutputStream(), PolicePersonWarnRecord.class).autoCloseStream(Boolean.FALSE).sheet("数据导出")
                .doWrite(records);
    }
}
