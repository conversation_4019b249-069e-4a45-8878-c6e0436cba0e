package com.hl.archive.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hl.archive.domain.dto.ImportResultDTO;
import com.hl.archive.domain.entity.PoliceQualificationRecord;
import com.hl.archive.mapper.PoliceQualificationRecordMapper;
import com.hl.archive.utils.SsoCacheUtil;
import com.hl.security.UserUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class PoliceQualificationRecordService extends ServiceImpl<PoliceQualificationRecordMapper, PoliceQualificationRecord> {

    /**
     * 导入Excel数据
     *
     * @param file Excel文件
     * @param category 资格类目
     * @return 导入结果
     */
    public ImportResultDTO importData(MultipartFile file, String category) {
        ImportResultDTO result = new ImportResultDTO();

        try {
            // 读取Excel文件
            ExcelReader reader = ExcelUtil.getReader(file.getInputStream());

            // 从第2行开始读取数据（第1行是表头）
            List<List<Object>> rows = reader.read(1);
            result.setTotalCount(rows.size());

            if (rows.isEmpty()) {
                result.addErrorMessage("Excel文件中没有数据");
                return result;
            }

            List<PoliceQualificationRecord> recordsToSave = new ArrayList<>();

            Date now = new Date();

            // 逐行处理数据
            for (int i = 0; i < rows.size(); i++) {
                List<Object> row = rows.get(i);
                int rowNum = i + 2; // Excel行号（从1开始，加上表头）

                try {
                    PoliceQualificationRecord record = parseRowData(row, category, rowNum);
                    if (record != null) {
                        // 设置审计字段
                        record.setCreatedBy(UserUtils.getUser().getIdCard());
                        record.setCreatedAt(now);
                        record.setUpdatedBy(UserUtils.getUser().getIdCard());
                        record.setUpdatedAt(now);
                        record.setStatus((byte) 0); // 默认有效
                        recordsToSave.add(record);
                    }
                } catch (Exception e) {
                    result.addErrorMessage(String.format("第%d行数据解析失败: %s", rowNum, e.getMessage()));
                    log.error("解析第{}行数据失败", rowNum, e);
                }
            }

            // 批量保存数据
            if (!recordsToSave.isEmpty()) {
                boolean saveResult = saveBatch(recordsToSave);
                if (saveResult) {
                    result.setSuccessCount(recordsToSave.size());
                    log.info("成功导入{}条记录", recordsToSave.size());
                } else {
                    result.addErrorMessage("批量保存数据失败");
                }
            }

        } catch (IOException e) {
            result.addErrorMessage("读取Excel文件失败: " + e.getMessage());
            log.error("读取Excel文件失败", e);
        } catch (Exception e) {
            result.addErrorMessage("导入过程中发生异常: " + e.getMessage());
            log.error("导入过程中发生异常", e);
        }

        return result;
    }

    /**
     * 解析行数据
     */
    private PoliceQualificationRecord parseRowData(List<Object> row, String category, int rowNum) {
        if (row == null || row.isEmpty()) {
            throw new RuntimeException("行数据为空");
        }

        PoliceQualificationRecord record = new PoliceQualificationRecord();

        try {

            // 单位
            if (row.size() > 0 && row.get(0) != null) {
                String trim = row.get(0).toString().trim();
                String organizationId = SsoCacheUtil.getOrganizationIdByName(trim);
                record.setOrganizationId(organizationId);
            }

            // 姓名（必填）
            if (row.size() > 1 && row.get(1) != null) {
                String trim = row.get(1).toString().trim();
                record.setName(trim);
                if (StrUtil.isBlank(record.getName())) {
                    throw new RuntimeException("姓名不能为空");
                }
                // 根据姓名 和单位 找到 身份证号
                List<JSONObject> idCardByNameAndOrganizationName = SsoCacheUtil.getIdCardByNameAndOrganizationName(trim, record.getOrganizationId());
                if (idCardByNameAndOrganizationName.size() > 1) {
                    throw new RuntimeException("找到多个身份证号，请检查姓名和单位是否正确");
                } else if (idCardByNameAndOrganizationName.size() == 1) {
                    record.setIdCard(idCardByNameAndOrganizationName.get(0).getString("id_card"));
                } else {
                    throw new RuntimeException("未找到身份证号，请检查姓名和单位是否正确");
                }
            }





            // 资格类目
            record.setCategory(category);

            // 具体项目
            if (row.size() > 2 && row.get(2) != null) {
                record.setProject(row.get(2).toString().trim());
            }

            // 记录日期
            if (row.size() > 3 && row.get(3) != null) {
                try {
                    if (row.get(3) instanceof Date) {
                        record.setIssueDate((Date) row.get(3));
                    } else {
                        record.setIssueDate(DateUtil.parse(row.get(3).toString()));
                    }
                } catch (Exception e) {
                    log.warn("第{}行记录日期格式错误: {}", rowNum, row.get(3));
                }
            }
            // 成绩
            if (row.size() > 4 && row.get(4) != null) {
                record.setScore(row.get(4).toString().trim());
            }

        } catch (Exception e) {
            throw new RuntimeException("数据格式错误: " + e.getMessage());
        }

        return record;
    }

}
