package com.hl.archive.service;

import cn.hutool.core.util.StrUtil;
import com.hl.archive.domain.dto.PoliceWorkQualityCountReturnDTO;
import com.hl.archive.domain.dto.WorkQualityCountQueryDTO;
import com.hl.archive.search.document.*;
import com.hl.archive.search.mapper.*;
import com.hl.archive.utils.MeasureUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class PoliceWorkQualityCountService {


    private final ViewCaseInfoTaskDocumentMapper viewCaseInfoTaskDocumentMapper;

    private final ViewCaseMeasureStatDocumentMapper viewCaseMeasureStatDocumentMapper;

    private final ViewCaseExamineTaskDocumentMapper viewCaseExamineTaskDocumentMapper;

    private final ViewPoliceExamineTaskDocumentMapper viewPoliceExamineTaskDocumentMapper;

    private final ViewCasePlaceExamineTaskDocumentMapper viewCasePlaceExamineTaskDocumentMapper;

    private final ViewTaskTimeoutResultDocumentMapper viewTaskTimeoutResultDocumentMapper;

    /**
     * 主办案件数量
     *
     * @param queryDTO 查询参数
     * @return 数量
     */
    public Long countZBAJ(WorkQualityCountQueryDTO queryDTO) {
        LambdaEsQueryWrapper<ViewCaseInfoTaskDocument> queryWrapper = new LambdaEsQueryWrapper<>();
        if (StrUtil.isNotBlank(queryDTO.getPoliceNumber())) {
            queryWrapper.like(ViewCaseInfoTaskDocument::getWorkPerson, queryDTO.getPoliceNumber());
        }
        Long l = viewCaseInfoTaskDocumentMapper.selectCount(queryWrapper);
        return l;
    }

    /**
     * 协办案件数量
     *
     * @param queryDTO 查询参数
     * @return 数量
     */
    public Long countXBAJ(WorkQualityCountQueryDTO queryDTO) {
        LambdaEsQueryWrapper<ViewCaseInfoTaskDocument> queryWrapper = new LambdaEsQueryWrapper<>();
        if (StrUtil.isNotBlank(queryDTO.getPoliceNumber())) {
            queryWrapper.like(ViewCaseInfoTaskDocument::getCoworkPerson, queryDTO.getPoliceNumber());
        }
        Long l = viewCaseInfoTaskDocumentMapper.selectCount(queryWrapper);
        return l;
    }

    /**
     * 案件措施统计
     *
     * @param queryDTO
     */
    public PoliceWorkQualityCountReturnDTO countAJCS(WorkQualityCountQueryDTO queryDTO) {
        PoliceWorkQualityCountReturnDTO qualityCountReturnDTO = new PoliceWorkQualityCountReturnDTO();

        PoliceWorkQualityCountReturnDTO.ZaCaseStat zaCaseStat = new PoliceWorkQualityCountReturnDTO.ZaCaseStat();
        PoliceWorkQualityCountReturnDTO.XsCaseStat xsCaseStat = new PoliceWorkQualityCountReturnDTO.XsCaseStat();
        qualityCountReturnDTO.setZaCaseStat(zaCaseStat);
        qualityCountReturnDTO.setXsCaseStat(xsCaseStat);
        LambdaEsQueryWrapper<ViewCaseMeasureStatDocument> queryWrapper = new LambdaEsQueryWrapper<>();
        if (StrUtil.isNotBlank(queryDTO.getPoliceNumber())) {
            queryWrapper.and(w -> w
                    .like(ViewCaseMeasureStatDocument::getWorkPerson, queryDTO.getPoliceNumber())
                    .or()
                    .like(ViewCaseMeasureStatDocument::getCoworkPerson, queryDTO.getPoliceNumber()));
        }
        List<ViewCaseMeasureStatDocument> viewCaseMeasureStatDocuments = viewCaseMeasureStatDocumentMapper.selectList(queryWrapper);
        if (!viewCaseMeasureStatDocuments.isEmpty()) {
            Map<Integer, List<ViewCaseMeasureStatDocument>> map = viewCaseMeasureStatDocuments.parallelStream()
                    .collect(Collectors.groupingBy(ViewCaseMeasureStatDocument::getIsCrime));
            if (map.containsKey(1)) {
                // 治安案件的
                List<ViewCaseMeasureStatDocument> data = map.get(1);
                data.forEach(m -> {
                            String resultCode = m.getResultCode();
                            if (StrUtil.isNotBlank(resultCode)) {
                                if (MeasureUtils.XING_ZHENG_JU_LIU.contains(resultCode)) {
                                    Long xzjlCount = zaCaseStat.getXzjlCount();
                                    xzjlCount++;
                                    zaCaseStat.setXzjlCount(xzjlCount);
                                }
                                if (MeasureUtils.XING_ZHENG_CHUAN_HUAN.contains(resultCode)) {
                                    Long chCount = zaCaseStat.getChCount();
                                    chCount++;
                                    zaCaseStat.setChCount(chCount);
                                }
                                if (MeasureUtils.JING_GAO.contains(resultCode)) {
                                    Long jgCount = zaCaseStat.getJgCount();
                                    jgCount++;
                                    zaCaseStat.setJgCount(jgCount);
                                }
                                if (MeasureUtils.FA_KUAN.contains(resultCode)) {
                                    Long fkCount = zaCaseStat.getFkCount();
                                    fkCount++;
                                    zaCaseStat.setFkCount(fkCount);
                                }
                                if (MeasureUtils.BU_YU_CHU_FA.contains(resultCode)) {
                                    Long bycfCount = zaCaseStat.getBycfCount();
                                    bycfCount++;
                                    zaCaseStat.setBycfCount(bycfCount);
                                }
                            }
                        });
            }

            if (map.containsKey(2)) {
                // 刑事案件
                List<ViewCaseMeasureStatDocument> caseMeasureStatDocuments = map.get(2);
                caseMeasureStatDocuments.forEach(m -> {
                            String resultCode = m.getResultCode();
                            if (StrUtil.isNotBlank(resultCode)) {
                                if (MeasureUtils.XING_SHI_CHUAN_HUAN.contains(resultCode)) {
                                    Long xschCount = xsCaseStat.getXschCount();
                                    xschCount++;
                                    xsCaseStat.setXschCount(xschCount);
                                }
                                if (MeasureUtils.JIAN_SHI_JU_ZHU.contains(resultCode)) {
                                    Long jsjzCount = xsCaseStat.getJsjzCount();
                                    jsjzCount++;
                                    xsCaseStat.setJsjzCount(jsjzCount);
                                }

                                if (MeasureUtils.XING_SHI_JU_LIU.contains(resultCode)) {
                                    Long xsjlCount = xsCaseStat.getXsjlCount();
                                    xsjlCount++;
                                    xsCaseStat.setJsjzCount(xsjlCount);
                                }

                                if (MeasureUtils.QU_BAO_HOU_SHEN.contains(resultCode)) {
                                    Long qbhsCount = xsCaseStat.getQbhsCount();
                                    qbhsCount++;
                                    xsCaseStat.setQbhsCount(qbhsCount);
                                }

                                if (MeasureUtils.DAI_BU.contains(resultCode)) {
                                    Long dbCount = xsCaseStat.getDbCount();
                                    dbCount++;
                                    xsCaseStat.setDbCount(dbCount);
                                }
                                if (MeasureUtils.YI_SONG_ZHI_SU.contains(resultCode)) {
                                    Long yszsCount = xsCaseStat.getYszsCount();
                                    yszsCount++;
                                    xsCaseStat.setYszsCount(yszsCount);
                                }

                                if (MeasureUtils.TI_QING_QI_SU.contains(resultCode)) {
                                    Long tqqsCount = xsCaseStat.getTqqsCount();
                                    tqqsCount++;
                                    xsCaseStat.setTqqsCount(tqqsCount);
                                }
                            }
                        });
            }

        }
        return qualityCountReturnDTO;
    }


    /**
     * 案件考评数量统计
     *
     * @param queryDTO 查询参数
     * @return 数量
     */
    public Long caseExamineCount(WorkQualityCountQueryDTO queryDTO) {

        LambdaEsQueryWrapper<ViewCaseExamineTaskDocument> queryWrapper = new LambdaEsQueryWrapper<>();
        if (StrUtil.isNotBlank(queryDTO.getPoliceNumber())) {
            // 警号查询
            queryWrapper.and(w -> w
                    .like(ViewCaseExamineTaskDocument::getWorkPerson, queryDTO.getPoliceNumber())
                    .or()
                    .like(ViewCaseExamineTaskDocument::getCoworkPerson, queryDTO.getPoliceNumber()));
        }

        Long l = viewCaseExamineTaskDocumentMapper
                .selectCount(queryWrapper);
        return l;
    }


    /**
     * 警情考评数量
     *
     * @param queryDTO 查询参数
     * @return 数量
     */
    public Long policeExamineCount(WorkQualityCountQueryDTO queryDTO) {
        LambdaEsQueryWrapper<ViewPoliceExamineTaskDocument> queryWrapper = new LambdaEsQueryWrapper<>();
        if (StrUtil.isNotBlank(queryDTO.getPoliceNumber())) {
            // 警号查询
            queryWrapper.and(w -> w
                    .like(ViewPoliceExamineTaskDocument::getWorkPerson, queryDTO.getPoliceNumber())
                    .or()
                    .like(ViewPoliceExamineTaskDocument::getCoworkPerson, queryDTO.getPoliceNumber()));
        }
        Long l = viewPoliceExamineTaskDocumentMapper
                .selectCount(queryWrapper);
        return l;
    }


    /**
     * 办案场所考评
     *
     * @param queryDTO 查询参数
     * @return 数量
     */
    public Long casePlaceExamineCounr(WorkQualityCountQueryDTO queryDTO) {
        LambdaEsQueryWrapper<ViewCasePlaceExamineTaskDocument> queryWrapper = new LambdaEsQueryWrapper<>();
        if (StrUtil.isNotBlank(queryDTO.getPoliceNumber())) {
            queryWrapper.and(w ->
                    w.like(ViewCasePlaceExamineTaskDocument::getWorkPerson, queryDTO.getPoliceNumber())
                            .or()
                            .like(ViewCasePlaceExamineTaskDocument::getCoworkPerson, queryDTO.getPoliceNumber()));
        }

        Long l = viewCasePlaceExamineTaskDocumentMapper.selectCount(queryWrapper);
        return l;

    }

    public PoliceWorkQualityCountReturnDTO timeoutCount(WorkQualityCountQueryDTO queryDTO) {

        AtomicReference<Long> signTimeout = new AtomicReference<>(0L);
        AtomicReference<Long> workTimeout = new AtomicReference<>(0L);
        LambdaEsQueryWrapper<ViewTaskTimeoutResultDocument> queryWrapper = new LambdaEsQueryWrapper<>();
        if (StrUtil.isNotBlank(queryDTO.getPoliceNumber())) {
            queryWrapper.and(w -> w
                    .like(ViewTaskTimeoutResultDocument::getWorkPerson, queryDTO.getPoliceNumber())
                    .or()
                    .like(ViewTaskTimeoutResultDocument::getCoworkPerson, queryDTO.getPoliceNumber()));
        }
        List<ViewTaskTimeoutResultDocument> viewTaskTimeoutResultDocuments = viewTaskTimeoutResultDocumentMapper.selectList(queryWrapper);

        viewTaskTimeoutResultDocuments.parallelStream()
                .forEach(viewTaskTimeoutResultDocument -> {
                    String timeoutType = viewTaskTimeoutResultDocument.getTimeoutType();
                    if (StrUtil.isNotBlank(timeoutType)) {
                        if (timeoutType.contains("sign")) {
                            signTimeout.getAndSet(signTimeout.get() + 1);
                        }
                        if (timeoutType.contains("work")) {
                            workTimeout.getAndSet(workTimeout.get() + 1);
                        }
                    }
                });

        PoliceWorkQualityCountReturnDTO qualityCountReturnDTO = new PoliceWorkQualityCountReturnDTO();
        qualityCountReturnDTO.setSignTimeoutCount(signTimeout.get());
        qualityCountReturnDTO.setWorkTimeoutCount(workTimeout.get());
        return qualityCountReturnDTO;
    }
}
