package com.hl.archive.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hl.archive.domain.dto.PoliceHonorAddDTO;
import com.hl.archive.domain.entity.PoliceHonors;
import com.hl.archive.mapper.PoliceHonorsMapper;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
@Service
public class PoliceHonorsService extends ServiceImpl<PoliceHonorsMapper, PoliceHonors> {

    public boolean addHonor(PoliceHonorAddDTO requestDTO) {
        List<String> idCards = requestDTO.getIdCards();
        List<PoliceHonors> policeHonorsList = new ArrayList<>();
        for (String idCard : idCards) {
            PoliceHonors policeHonors = new PoliceHonors();
            policeHonors.setIdCard(idCard);
            policeHonors.setHonorName(requestDTO.getHonorName());
            policeHonors.setAwardDate(requestDTO.getAwardDate());
            policeHonors.setSourceType(requestDTO.getSourceType());
            policeHonors.setApproveAuthority(requestDTO.getApproveAuthority());
            policeHonors.setBh(requestDTO.getBh());
            policeHonors.setFiles(requestDTO.getFiles());
            policeHonorsList.add(policeHonors);
        }
        return this.saveBatch(policeHonorsList);
    }
}
