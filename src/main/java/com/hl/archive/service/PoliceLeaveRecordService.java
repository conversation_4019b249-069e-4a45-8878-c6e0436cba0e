package com.hl.archive.service;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hl.archive.domain.entity.PoliceBasicInfo;
import com.hl.archive.listener.RefreshDutyStatusEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hl.archive.mapper.PoliceLeaveRecordMapper;
import com.hl.archive.domain.entity.PoliceLeaveRecord;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

@Service
@Slf4j
public class PoliceLeaveRecordService extends ServiceImpl<PoliceLeaveRecordMapper, PoliceLeaveRecord> {


    @Resource
    private PoliceBasicInfoService policeBasicInfoService;


    @EventListener(RefreshDutyStatusEvent.class)
    @Scheduled(cron = "0 10 0 * * *")
    public void refreshLeaveRecord() {
        log.info("接收刷新在岗状态事件");

        DateTime start = DateUtil.endOfDay(DateUtil.date());
        DateTime end = DateUtil.beginOfDay(DateUtil.date());

        List<String> idCards = this.baseMapper.queryLeaveRecord(start.toString(), end.toString());
        log.info("当天休假身份证:{}", idCards);
        policeBasicInfoService.update(Wrappers.<PoliceBasicInfo>lambdaUpdate()
                .set(PoliceBasicInfo::getDutyStatus, 0)
                .notIn(PoliceBasicInfo::getIdCard, idCards));

        policeBasicInfoService.update(Wrappers.<PoliceBasicInfo>lambdaUpdate()
                .set(PoliceBasicInfo::getDutyStatus, 1)
                .in(PoliceBasicInfo::getIdCard, idCards));

    }

}
