package com.hl.archive.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hl.archive.domain.entity.PoliceLeaveRecord;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface PoliceLeaveRecordMapper extends BaseMapper<PoliceLeaveRecord> {

    @Select("select distinct id_card from police_leave_record where start_date <= #{startDate} and end_date >= #{endDate}")
    List<String> queryLeaveRecord(String startDate,String endDate);
}