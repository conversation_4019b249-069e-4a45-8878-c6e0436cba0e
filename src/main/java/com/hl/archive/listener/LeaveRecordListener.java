package com.hl.archive.listener;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.hl.archive.domain.entity.PoliceLeaveRecord;
import com.hl.archive.feign.TaskApi;
import com.hl.archive.service.PoliceLeaveRecordService;
import com.hl.common.domain.R;
import com.hl.security.config.sso.api.SsoCacheApi;
import com.rabbitmq.client.Channel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

@Component
@Slf4j
@RequiredArgsConstructor
public class LeaveRecordListener {

    @Value("${spring.security.sso.projectToken}")
    private String token;


    @Resource
    private PoliceLeaveRecordService policeLeaveRecordService;

    @Resource
    private TaskApi taskApi;

    @Resource
    private SsoCacheApi ssoCacheApi;

    private final ApplicationEventPublisher eventPublisher;


    @RabbitListener(queues = "wj_task_archive_s")
    public R<?> rabbitListener(Message message, Channel channel) throws IOException {
        try {
            String msgBody = new String(message.getBody(), StandardCharsets.UTF_8);
            log.info("接收到消息: {}", msgBody);
            ThreadUtil.execAsync(() -> {
                handleData(msgBody);
            });
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            // 处理失败，是否需要重试或丢弃可根据业务设定
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        }
        return R.ok();
    }


    private void handleData(String msg) {
        try {
            JSONObject data = JSONObject.parseObject(msg);
            JSONObject contentData = JSONObject.parseObject(data.getString("content_data"));
            String opt = contentData.getString("opt");
            if (!"audit".equals(opt)) {
                return;
            }
            String configUuid = contentData.getByPath("data.config_uuid").toString();

            switch (configUuid) {
//                case "C1FI31VEE7X":
                case "CC42ITDEHRQ":
                    // 请休假
                    praseQXJ(contentData);
                    addLCBB(contentData, "CC42ITDEHRQ");
                    break;
                case "CG9Z9HJ3FJW":
                    // 出差
                    praseCC(contentData);
                    addLCBB(contentData, "CG9Z9HJ3FJW");
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }


    }

    // 请休假
    private void praseQXJ(JSONObject contentData) {
        String passStatus = contentData.getByPath("data.content.pass").toString();
        if (!"1".equals(passStatus)) {
            return;
        }
        String taskId = contentData.getByPath("data.task_id").toString();
        JSONObject param = new JSONObject();
        param.put("task_id", taskId);
        R<?> oneTask = taskApi.getOneTask(token, param);
        JSONObject parsed = JSONObject.from(oneTask.getData());
        log.info("parsed--->{}", parsed.getByPath("content"));

        String time = parsed.getByPath("content.qjkssj").toString();
        String[] timeArr = time.split("_");
        String qjkssj = timeArr[0];
        String qjjssj = timeArr[1];
//        String qjkssj = parsed.getByPath("content.qjkssj").toString();
//        String qjjssj = parsed.getByPath("content.qjjssj").toString();
        String idCard = parsed.getByPath("all_content.id_card").toString();
        PoliceLeaveRecord leaveRecord = new PoliceLeaveRecord();
        leaveRecord.setStartDate(DateUtil.beginOfDay(DateUtil.parseDate(qjkssj)));
        leaveRecord.setEndDate(DateUtil.endOfDay(DateUtil.parseDate(qjjssj)));
        leaveRecord.setIdCard(idCard);
        leaveRecord.setLeaveType("请休假");
        policeLeaveRecordService.save(leaveRecord);
        eventPublisher.publishEvent(new RefreshDutyStatusEvent());

    }

    // 出差
    private void praseCC(JSONObject contentData) {
        String passStatus = contentData.getByPath("data.content.pass").toString();
        if (!"1".equals(passStatus)) {
            return;
        }
        String taskId = contentData.getByPath("data.task_id").toString();
        JSONObject param = new JSONObject();
        param.put("task_id", taskId);
        R<?> oneTask = taskApi.getOneTask(token, param);
        JSONObject parsed = JSONObject.from(oneTask.getData());
        String ccsj = parsed.getByPath("content.ccsj").toString();
        String[] timeArr = ccsj.split("_");

        String idCard = parsed.getByPath("all_content.id_card").toString();
        PoliceLeaveRecord leaveRecord = new PoliceLeaveRecord();
        leaveRecord.setStartDate(DateUtil.beginOfDay(DateUtil.parseDate(timeArr[0])));
        leaveRecord.setEndDate(DateUtil.endOfDay(DateUtil.parseDate(timeArr[1])));
        leaveRecord.setIdCard(idCard);
        leaveRecord.setLeaveType("出差");
        policeLeaveRecordService.save(leaveRecord);
        eventPublisher.publishEvent(new RefreshDutyStatusEvent());

    }

    //添加离常报备
    private void addLCBB(JSONObject contentData, String type) {
        String passStatus = contentData.getByPath("data.content.pass").toString();
        if (!"1".equals(passStatus)) {
            return;
        }
        String taskId = contentData.getByPath("data.task_id").toString();
        JSONObject param = new JSONObject();
        param.put("task_id", taskId);
        R<?> oneTask = taskApi.getOneTask(token, param);
        JSONObject parsed = JSONObject.from(oneTask.getData());
        String idCard = parsed.getByPath("all_content.id_card").toString();

        String customId = contentData.getByPath("data.custom_id").toString();
        log.info("customId------> {}", customId);
        String zwjb = parsed.getByPath("content.zwjb").toString();
        final Map<String, String> APPROVAL_MAPPING = new HashMap<>();
        {
            APPROVAL_MAPPING.put("ZGLDSP", "普通民警");
            APPROVAL_MAPPING.put("FGJLD", "股级干部");
            APPROVAL_MAPPING.put("JLDSP", "科级干部");
        }
        if (APPROVAL_MAPPING.getOrDefault(customId, "").equals(zwjb)) {
            JSONObject obj = new JSONObject();
            obj.put("config_uuid", "C09ZZEL29S8"); // 离常报备
            if ("CC42ITDEHRQ".equals(type)) { // 请休假
                if (parsed.getByPath("content.sflc").equals("否")) {
                    return;
                }
                obj.put("qjrxm", parsed.getByPath("content.xm")); // 姓名
                obj.put("qjrdw", parsed.getByPath("content.dw")); // 单位
                obj.put("qjrzw", parsed.getByPath("content.zw")); // 职务
                obj.put("qjrlxfs", ""); // 联系电话
                obj.put("wcsy", parsed.getByPath("content.qjsy")); // 离常事由-请假事由
                obj.put("title", parsed.getByPath("content.qjsy"));
                obj.put("lksj", parsed.getByPath("content.qjkssj")); // 外出时间
                obj.put("wcdd", parsed.getByPath("content.qjwcdd")); // 外出地点
                obj.put("lsdzryzw", parsed.getByPath("content.zwjb")); // 职务级别
                obj.put("lsdzry", parsed.getByPath("content.qjlsdzry")); // 临时代职人员
                obj.put("bz", ""); // 备注
                obj.put("sign_url", parsed.getByPath("content.sign_url"));
            } else if ("CG9Z9HJ3FJW".equals(type)) { // 出差
                obj.put("qjrxm", parsed.getByPath("content.brxm")); // 姓名
                obj.put("qjrdw", parsed.getByPath("content.brdw")); // 单位
                obj.put("qjrzw", parsed.getByPath("content.brzw")); // 职务
                obj.put("qjrlxfs", ""); // 联系电话
                obj.put("wcsy", parsed.getByPath("content.ccsy")); // 离常事由-出差事由 // TODO 类型字典转换
                obj.put("title", parsed.getByPath("content.ccsy"));
                obj.put("lksj", parsed.getByPath("content.ccsj")); // 外出时间
                obj.put("wcdd", parsed.getByPath("content.ccdd")); // 外出地点
                obj.put("lsdzryzw", parsed.getByPath("content.zwjb")); // 职务级别
                obj.put("lsdzry", parsed.getByPath("content.lsdzry")); // 临时代职人员
                obj.put("bz", parsed.getByPath("content.bz")); // 备注
                obj.put("sign_url", parsed.getByPath("content.sign_url"));
            }
            log.info("obj------> {}", obj);

            JSONObject loginObj = new JSONObject();
            loginObj.put("id_card", idCard);
            loginObj.put("login_type", "3");
            log.info("登录信息--->{}", loginObj);
            R<JSONObject> login = ssoCacheApi.login(loginObj);
            if (R.isError(login)) {
                log.error("登录失败");
            } else {
                String loginToken = login.getData().getString("token");
                R<JSONArray> r = taskApi.add(loginToken, obj);
                if (R.isSuccess(r)) {
                    log.info("添加了一个任务");
                }
            }
        }
    }


}
