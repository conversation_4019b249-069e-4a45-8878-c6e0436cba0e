package com.hl.archive.controller;

import com.hl.archive.domain.dto.PoliceWorkQualityCountReturnDTO;
import com.hl.archive.domain.dto.WorkQualityCountQueryDTO;
import com.hl.archive.service.PoliceWorkQualityCountService;
import com.hl.common.domain.R;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/policeWorkQuality")
@Slf4j
@RequiredArgsConstructor
public class WorkQualityCountController {

    private final PoliceWorkQualityCountService policeWorkQualityCountService;

    @PostMapping("/count")
    public R<?> count(@RequestBody WorkQualityCountQueryDTO queryDTO){
        PoliceWorkQualityCountReturnDTO qualityCountReturnDTO = new PoliceWorkQualityCountReturnDTO();
        // 刑事 治安 人员措施采取数量
        PoliceWorkQualityCountReturnDTO measures = policeWorkQualityCountService.countAJCS(queryDTO);
        qualityCountReturnDTO.setXsCaseStat(measures.getXsCaseStat());
        qualityCountReturnDTO.setZaCaseStat(measures.getZaCaseStat());
        // 协办案件数量
        Long xbCount = policeWorkQualityCountService.countXBAJ(queryDTO);
        qualityCountReturnDTO.setXbajCount(xbCount);
        // 主办案件数量
        Long zbCount = policeWorkQualityCountService.countZBAJ(queryDTO);
        qualityCountReturnDTO.setZbajCount(zbCount);
        // 案件考评数量
        Long caseExamineCount = policeWorkQualityCountService.caseExamineCount(queryDTO);
        qualityCountReturnDTO.setCaseExamineCount(caseExamineCount);
        // 警情考评数量
        Long policeExamineCount = policeWorkQualityCountService.policeExamineCount(queryDTO);
        qualityCountReturnDTO.setPoliceExamineCount(policeExamineCount);
        // 办案场所考评数量
        Long casePlaceExamineCount = policeWorkQualityCountService.casePlaceExamineCounr(queryDTO);
        qualityCountReturnDTO.setCasePlaceExamineCount(casePlaceExamineCount);

        // 超时统计
        PoliceWorkQualityCountReturnDTO timeoutCount = policeWorkQualityCountService.timeoutCount(queryDTO);
        qualityCountReturnDTO.setSignTimeoutCount(timeoutCount.getSignTimeoutCount());
        qualityCountReturnDTO.setWorkTimeoutCount(timeoutCount.getWorkTimeoutCount());

        return R.ok(qualityCountReturnDTO);
    }

}
