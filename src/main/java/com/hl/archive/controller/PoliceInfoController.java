package com.hl.archive.controller;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.data.Pictures;
import com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy;
import com.deepoove.poi.util.PoitlIOUtils;
import com.hl.archive.domain.dto.CareerEvent;
import com.hl.archive.domain.dto.PoliceStatisticsDTO;
import com.hl.archive.domain.entity.*;
import com.hl.archive.service.PoliceHeartToHeartVisitService;
import com.hl.archive.domain.request.PoliceBaseQueryRequest;
import com.hl.archive.domain.request.StatisticsQueryRequest;
import com.hl.archive.domain.request.StatisticsDrillDownRequest;
import com.hl.archive.service.*;
import com.hl.common.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.io.BufferedOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.time.ZoneId;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.servlet.http.HttpServletResponse;

@RequestMapping("/police")
@RestController
@Slf4j
@RequiredArgsConstructor
@Api(tags = "民警档案")
public class PoliceInfoController {

    private final PoliceBasicInfoService policeBasicInfoService;

    private final PolicePositionRankService policePositionRankService;

    private final PoliceRankInfoService policeRankInfoService;

    private final PoliceContactInfoService policeContactInfoService;

    private final PolicePoliticalStatusService policePoliticalStatusService;

    private final PoliceEducationService policeEducationService;

    private final PoliceResumeService policeResumeService;

    private final PoliceTrainingService policeTrainingService;

    private final PoliceAnnualAssessmentService policeAnnualAssessmentService;

    private final PoliceQuarterlyAssessmentService policeQuarterlyAssessmentService;

    private final PoliceMonthlyAssessmentService policeMonthlyAssessmentService;

    private final PoliceFamilyMembersService policeFamilyMembersService;

    private final PoliceHonorsService policeHonorsService;

    private final PoliceSpecialtiesService policeSpecialtiesService;

    private final PoliceMarriageStatusService policeMarriageStatusService;

    private final PolicePassportService policePassportService;

    private final PoliceHkMacauTaiwanPermitService policeHkMacauTaiwanPermitService;

    private final PoliceHkMacauTaiwanTravelService policeHkMacauTaiwanTravelService;

    private final PoliceLoanInfoService policeLoanInfoService;

    private final PoliceWeddingFuneralEventsService policeWeddingFuneralEventsService;

    private final PoliceInvestmentInfoService policeInvestmentInfoService;

    private final PoliceHealthStatusService policeHealthStatusService;

    private final PoliceOrganizationalInquiryService policeOrganizationalInquiryService;

    private final PoliceChildrenForeignMarriageService policeChildrenForeignMarriageService;

    private final PoliceFamilyOverseasMigrationService policeFamilyOverseasMigrationService;

    private final PoliceFamilyCriminalLiabilityService policeFamilyCriminalLiabilityService;

    private final PoliceFamilyBusinessService policeFamilyBusinessService;

    private final PoliceFamilyPaidInstitutionsService policeFamilyPaidInstitutionsService;

    private final PoliceFamilyPrivateEquityFundService policeFamilyPrivateEquityFundService;

    private final PoliceFamilyRealEstateService policeFamilyRealEstateService;

    private final PoliceFamilyVehiclesService policeFamilyVehiclesService;

    private final PoliceFamilySecuritiesInsuranceService policeFamilySecuritiesInsuranceService;

    private final PoliceOtherMattersService policeOtherMattersService;

    private final PoliceDishonestExecutorService policeDishonestExecutorService;

    private final PoliceOnlineExamService policeOnlineExamService;

    private final PoliceTrainingRecordsService policeTrainingRecordsService;


    @PostMapping("/basicInfo")
    @ApiOperation("获取民警基本信息")
    public R<PoliceBasicInfo> getBasicInfo(@RequestBody PoliceBaseQueryRequest request) {
        PoliceBasicInfo one = policeBasicInfoService.lambdaQuery()
                .eq(PoliceBasicInfo::getIdCard, request.getIdCard())
                .last("limit 1").one();
        if (one == null) {
            return R.fail("未查询到该民警信息");
        }
        return R.ok(one);
    }

    @PostMapping("/addBasicInfo")
    @ApiOperation("添加民警基本信息")
    public R<Boolean> addBasicInfo(@RequestBody PoliceBasicInfo request) {
        return R.ok(policeBasicInfoService.save(request));
    }

    @PostMapping("/updateBasicInfo")
    @ApiOperation("更新民警基本信息")
    public R<Boolean> updateBasicInfo(@RequestBody PoliceBasicInfo request) {
        return R.ok(policeBasicInfoService.updateById(request));
    }

    @PostMapping("/deleteBasicInfo")
    @ApiOperation("删除民警基本信息")
    public R<Boolean> deleteBasicInfo(@RequestBody PoliceBasicInfo request) {
        return R.ok(policeBasicInfoService.removeById(request));
    }

    @PostMapping("/listPositionRank")
    @ApiOperation(value = "获取职务职级",
            tags = "档案列表")
    public R<List<PolicePositionRank>> listPositionRank(@RequestBody PoliceBaseQueryRequest request) {

        Page<PolicePositionRank> page = policePositionRankService.page(Page.of(request.getPage(), request.getLimit()),
                Wrappers.<PolicePositionRank>lambdaQuery()
                        .eq(PolicePositionRank::getIdCard, request.getIdCard())
                        .orderByDesc(PolicePositionRank::getCurrentPositionDate));
        return R.ok(page.getRecords(), (int) page.getTotal());
    }

    @PostMapping("/addPositionRank")
    @ApiOperation("添加职务职级")
    public R<Boolean> addPositionRank(@RequestBody PolicePositionRank request) {
        return R.ok(policePositionRankService.save(request));
    }

    @PostMapping("/updatePositionRank")
    @ApiOperation("更新职务职级")
    public R<Boolean> updatePositionRank(@RequestBody PolicePositionRank request) {
        return R.ok(policePositionRankService.updateById(request));
    }

    @PostMapping("/deletePositionRank")
    @ApiOperation("删除职务职级")
    public R<Boolean> deletePositionRank(@RequestBody PolicePositionRank request) {
        return R.ok(policePositionRankService.removeById(request));
    }

    @PostMapping("/listRankInfo")
    @ApiOperation(value = "获取警衔信息",
            tags = "档案列表")
    public R<List<PoliceRankInfo>> listRankInfo(@RequestBody PoliceBaseQueryRequest request) {

        Page<PoliceRankInfo> page = policeRankInfoService.page(Page.of(request.getPage(), request.getLimit()),
                Wrappers.<PoliceRankInfo>lambdaQuery()
                        .eq(PoliceRankInfo::getIdCard, request.getIdCard())
                        .orderByDesc(PoliceRankInfo::getPromotionDate));
        return R.ok(page.getRecords(), (int) page.getTotal());
    }

    @PostMapping("/addRankInfo")
    @ApiOperation("添加警衔信息")
    public R<Boolean> addRankInfo(@RequestBody PoliceRankInfo request) {
        return R.ok(policeRankInfoService.save(request));
    }

    @PostMapping("/updateRankInfo")
    @ApiOperation("更新警衔信息")
    public R<Boolean> updateRankInfo(@RequestBody PoliceRankInfo request) {
        return R.ok(policeRankInfoService.updateById(request));
    }

    @PostMapping("/deleteRankInfo")
    @ApiOperation("删除警衔信息")
    public R<Boolean> deleteRankInfo(@RequestBody PoliceRankInfo request) {
        return R.ok(policeRankInfoService.removeById(request));
    }

    @PostMapping("/listContactInfo")
    @ApiOperation(value = "家庭住址通信方式",
            tags = "档案列表")
    public R<List<PoliceContactInfo>> listContactInfo(@RequestBody PoliceBaseQueryRequest request) {
        Page<PoliceContactInfo> page = policeContactInfoService.page(Page.of(request.getPage(), request.getLimit()),
                Wrappers.<PoliceContactInfo>lambdaQuery()
                        .eq(PoliceContactInfo::getIdCard, request.getIdCard()));
        return R.ok(page.getRecords(), (int) page.getTotal());
    }

    @PostMapping("/addContactInfo")
    @ApiOperation("添加家庭住址通信方式")
    public R<Boolean> addContactInfo(@RequestBody PoliceContactInfo request) {
        return R.ok(policeContactInfoService.save(request));
    }

    @PostMapping("/updateContactInfo")
    @ApiOperation("更新家庭住址通信方式")
    public R<Boolean> updateContactInfo(@RequestBody PoliceContactInfo request) {
        return R.ok(policeContactInfoService.updateById(request));
    }

    @PostMapping("/deleteContactInfo")
    @ApiOperation("删除家庭住址通信方式")
    public R<Boolean> deleteContactInfo(@RequestBody PoliceContactInfo request) {
        return R.ok(policeContactInfoService.removeById(request));
    }

    @PostMapping("/listPoliticalStatus")
    @ApiOperation(value = "政治面貌",
            tags = "档案列表")
    public R<List<PolicePoliticalStatus>> listPoliticalStatus(@RequestBody PoliceBaseQueryRequest request) {
        Page<PolicePoliticalStatus> page = policePoliticalStatusService.page(Page.of(request.getPage(), request.getLimit()),
                Wrappers.<PolicePoliticalStatus>lambdaQuery()
                        .eq(PolicePoliticalStatus::getIdCard, request.getIdCard()));
        return R.ok(page.getRecords(), (int) page.getTotal());
    }

    @PostMapping("/addPoliticalStatus")
    @ApiOperation("添加政治面貌")
    public R<Boolean> addPoliticalStatus(@RequestBody PolicePoliticalStatus request) {
        return R.ok(policePoliticalStatusService.save(request));
    }

    @PostMapping("/updatePoliticalStatus")
    @ApiOperation("更新政治面貌")
    public R<Boolean> updatePoliticalStatus(@RequestBody PolicePoliticalStatus request) {
        return R.ok(policePoliticalStatusService.updateById(request));
    }

    @PostMapping("/deletePoliticalStatus")
    @ApiOperation("删除政治面貌")
    public R<Boolean> deletePoliticalStatus(@RequestBody PolicePoliticalStatus request) {
        return R.ok(policePoliticalStatusService.removeById(request));
    }

    @PostMapping("/listEducation")
    @ApiOperation(value = "学历学位",
            tags = "档案列表")
    public R<List<PoliceEducation>> listEducation(@RequestBody PoliceBaseQueryRequest request) {
        Page<PoliceEducation> page = policeEducationService.page(Page.of(request.getPage(), request.getLimit()),
                Wrappers.<PoliceEducation>lambdaQuery()
                        .eq(PoliceEducation::getIdCard, request.getIdCard())
                        .orderByDesc(PoliceEducation::getEnrollmentDate));
        return R.ok(page.getRecords(), (int) page.getTotal());
    }

    @PostMapping("/addEducation")
    @ApiOperation("添加学历学位")
    public R<Boolean> addEducation(@RequestBody PoliceEducation request) {
        return R.ok(policeEducationService.save(request));
    }

    @PostMapping("/updateEducation")
    @ApiOperation("更新学历学位")
    public R<Boolean> updateEducation(@RequestBody PoliceEducation request) {
        return R.ok(policeEducationService.updateById(request));
    }

    @PostMapping("/deleteEducation")
    @ApiOperation("删除学历学位")
    public R<Boolean> deleteEducation(@RequestBody PoliceEducation request) {
        return R.ok(policeEducationService.removeById(request));
    }

    @PostMapping("/listResume")
    @ApiOperation(value = "个人简历",
            tags = "档案列表")
    public R<List<PoliceResume>> listResume(@RequestBody PoliceBaseQueryRequest request) {
        Page<PoliceResume> page = policeResumeService.page(Page.of(request.getPage(), request.getLimit()),
                Wrappers.<PoliceResume>lambdaQuery()
                        .eq(PoliceResume::getIdCard, request.getIdCard())
                        .orderByDesc(PoliceResume::getStartDate));
        return R.ok(page.getRecords(), (int) page.getTotal());
    }

    @PostMapping("/addResume")
    @ApiOperation("添加个人简历")
    public R<Boolean> addResume(@RequestBody PoliceResume request) {
        return R.ok(policeResumeService.save(request));
    }

    @PostMapping("/updateResume")
    @ApiOperation("更新个人简历")
    public R<Boolean> updateResume(@RequestBody PoliceResume request) {
        return R.ok(policeResumeService.updateById(request));
    }

    @PostMapping("/deleteResume")
    @ApiOperation("删除个人简历")
    public R<Boolean> deleteResume(@RequestBody PoliceResume request) {
        return R.ok(policeResumeService.removeById(request));
    }

    @PostMapping("/listTraining")
    @ApiOperation(value = "教育培训",
            tags = "档案列表")
    public R<List<PoliceTraining>> listTraining(@RequestBody PoliceBaseQueryRequest request) {
        Page<PoliceTraining> page = policeTrainingService.page(Page.of(request.getPage(), request.getLimit()),
                Wrappers.<PoliceTraining>lambdaQuery()
                        .eq(PoliceTraining::getIdCard, request.getIdCard())
                        .orderByDesc(PoliceTraining::getTrainingStartDate));
        return R.ok(page.getRecords(), (int) page.getTotal());
    }

    @PostMapping("/addTraining")
    @ApiOperation("添加教育培训")
    public R<Boolean> addTraining(@RequestBody PoliceTraining request) {
        return R.ok(policeTrainingService.save(request));
    }

    @PostMapping("/updateTraining")
    @ApiOperation("更新教育培训")
    public R<Boolean> updateTraining(@RequestBody PoliceTraining request) {
        return R.ok(policeTrainingService.updateById(request));
    }

    @PostMapping("/deleteTraining")
    @ApiOperation("删除教育培训")
    public R<Boolean> deleteTraining(@RequestBody PoliceTraining request) {
        return R.ok(policeTrainingService.removeById(request));
    }

    @PostMapping("/listAnnualAssessment")
    @ApiOperation(value = "年度考核",
            tags = "档案列表")
    public R<List<PoliceAnnualAssessment>> listAnnualAssessment(@RequestBody PoliceBaseQueryRequest request) {
        Page<PoliceAnnualAssessment> page = policeAnnualAssessmentService.page(Page.of(request.getPage(), request.getLimit()),
                Wrappers.<PoliceAnnualAssessment>lambdaQuery()
                        .eq(PoliceAnnualAssessment::getIdCard, request.getIdCard())
                        .orderByDesc(PoliceAnnualAssessment::getAssessmentYear));
        return R.ok(page.getRecords(), (int) page.getTotal());
    }

    @PostMapping("/addAnnualAssessment")
    @ApiOperation("添加年度考核")
    public R<Boolean> addAnnualAssessment(@RequestBody PoliceAnnualAssessment request) {
        return R.ok(policeAnnualAssessmentService.save(request));
    }

    @PostMapping("/updateAnnualAssessment")
    @ApiOperation("更新年度考核")
    public R<Boolean> updateAnnualAssessment(@RequestBody PoliceAnnualAssessment request) {
        return R.ok(policeAnnualAssessmentService.updateById(request));
    }

    @PostMapping("/deleteAnnualAssessment")
    @ApiOperation("删除年度考核")
    public R<Boolean> deleteAnnualAssessment(@RequestBody PoliceAnnualAssessment request) {
        return R.ok(policeAnnualAssessmentService.removeById(request));
    }

    @PostMapping("/listQuarterlyAssessment")
    @ApiOperation(value = "季度考核",
            tags = "档案列表")
    public R<List<PoliceQuarterlyAssessment>> listQuarterlyAssessment(@RequestBody PoliceBaseQueryRequest request) {
        Page<PoliceQuarterlyAssessment> page = policeQuarterlyAssessmentService.page(Page.of(request.getPage(), request.getLimit()),
                Wrappers.<PoliceQuarterlyAssessment>lambdaQuery()
                        .eq(PoliceQuarterlyAssessment::getIdCard, request.getIdCard())
                        .orderByDesc(PoliceQuarterlyAssessment::getAssessmentYear));
        return R.ok(page.getRecords(), (int) page.getTotal());
    }

    @PostMapping("/addQuarterlyAssessment")
    @ApiOperation(value = "添加季度考核")
    public R<Boolean> addQuarterlyAssessment(@RequestBody PoliceQuarterlyAssessment request) {
        return R.ok(policeQuarterlyAssessmentService.save(request));
    }

    @PostMapping("/updateQuarterlyAssessment")
    @ApiOperation("更新季度考核")
    public R<Boolean> updateQuarterlyAssessment(@RequestBody PoliceQuarterlyAssessment request) {
        return R.ok(policeQuarterlyAssessmentService.updateById(request));
    }

    @PostMapping("/deleteQuarterlyAssessment")
    @ApiOperation("删除季度考核")
    public R<Boolean> deleteQuarterlyAssessment(@RequestBody PoliceQuarterlyAssessment request) {
        return R.ok(policeQuarterlyAssessmentService.removeById(request));
    }

    @PostMapping("/listMonthlyAssessment")
    @ApiOperation(value = "月度考核",
            tags = "档案列表")
    public R<List<PoliceMonthlyAssessment>> listMonthlyAssessment(@RequestBody PoliceBaseQueryRequest request) {
        Page<PoliceMonthlyAssessment> page = policeMonthlyAssessmentService.page(Page.of(request.getPage(), request.getLimit()),
                Wrappers.<PoliceMonthlyAssessment>lambdaQuery()
                        .eq(PoliceMonthlyAssessment::getIdCard, request.getIdCard())
                        .orderByDesc(PoliceMonthlyAssessment::getAssessmentYear));
        return R.ok(page.getRecords(), (int) page.getTotal());
    }

    @PostMapping("/addMonthlyAssessment")
    @ApiOperation("添加月度考核")
    public R<Boolean> addMonthlyAssessment(@RequestBody PoliceMonthlyAssessment request) {
        return R.ok(policeMonthlyAssessmentService.save(request));
    }

    @PostMapping("/updateMonthlyAssessment")
    @ApiOperation("更新月度考核")
    public R<Boolean> updateMonthlyAssessment(@RequestBody PoliceMonthlyAssessment request) {
        return R.ok(policeMonthlyAssessmentService.updateById(request));
    }

    @PostMapping("/deleteMonthlyAssessment")
    @ApiOperation("删除月度考核")
    public R<Boolean> deleteMonthlyAssessment(@RequestBody PoliceMonthlyAssessment request) {
        return R.ok(policeMonthlyAssessmentService.removeById(request));
    }

    @PostMapping("/listFamilyMembers")
    @ApiOperation(value = "家庭成员",
            tags = "档案列表")
    public R<List<PoliceFamilyMembers>> listFamilyMembers(@RequestBody PoliceBaseQueryRequest request) {
        Page<PoliceFamilyMembers> page = policeFamilyMembersService.page(Page.of(request.getPage(), request.getLimit()),
                Wrappers.<PoliceFamilyMembers>lambdaQuery()
                        .eq(PoliceFamilyMembers::getIdCard, request.getIdCard()));
        return R.ok(page.getRecords(), (int) page.getTotal());
    }

    @PostMapping("/addFamilyMembers")
    @ApiOperation(value = "添加家庭成员",
            tags = "档案列表")
    public R<Boolean> addFamilyMembers(@RequestBody PoliceFamilyMembers request) {
        return R.ok(policeFamilyMembersService.save(request));
    }

    @PostMapping("/updateFamilyMembers")
    @ApiOperation("更新家庭成员")
    public R<Boolean> updateFamilyMembers(@RequestBody PoliceFamilyMembers request) {
        return R.ok(policeFamilyMembersService.updateById(request));
    }

    @PostMapping("/deleteFamilyMembers")
    @ApiOperation("删除家庭成员")
    public R<Boolean> deleteFamilyMembers(@RequestBody PoliceFamilyMembers request) {
        return R.ok(policeFamilyMembersService.removeById(request));
    }

    @PostMapping("/listHonors")
    @ApiOperation(value = "表彰奖励",
            tags = "档案列表")
    public R<List<PoliceHonors>> listHonors(@RequestBody PoliceBaseQueryRequest request) {
        Page<PoliceHonors> page = policeHonorsService.page(Page.of(request.getPage(), request.getLimit()),
                Wrappers.<PoliceHonors>lambdaQuery()
                        .eq(PoliceHonors::getIdCard, request.getIdCard())
                        .orderByDesc(PoliceHonors::getAwardDate));
        return R.ok(page.getRecords(), (int) page.getTotal());
    }

    @PostMapping("/addHonors")
    @ApiOperation("添加表彰奖励")
    public R<Boolean> addHonors(@RequestBody PoliceHonors request) {
        return R.ok(policeHonorsService.save(request));
    }

    @PostMapping("/updateHonors")
    @ApiOperation("更新表彰奖励")
    public R<Boolean> updateHonors(@RequestBody PoliceHonors request) {
        return R.ok(policeHonorsService.updateById(request));
    }

    @PostMapping("/deleteHonors")
    @ApiOperation("删除表彰奖励")
    public R<Boolean> deleteHonors(@RequestBody PoliceHonors request) {
        return R.ok(policeHonorsService.removeById(request));
    }

    @PostMapping("/listSpecialties")
    @ApiOperation(value = "特长",
            tags = "档案列表")
    public R<List<PoliceSpecialties>> listSpecialties(@RequestBody PoliceBaseQueryRequest request) {
        Page<PoliceSpecialties> page = policeSpecialtiesService.page(Page.of(request.getPage(), request.getLimit()),
                Wrappers.<PoliceSpecialties>lambdaQuery()
                        .eq(PoliceSpecialties::getIdCard, request.getIdCard())
                        .orderByDesc(PoliceSpecialties::getAwardDate));
        return R.ok(page.getRecords(), (int) page.getTotal());
    }

    @PostMapping("/addSpecialties")
    @ApiOperation("添加特长")
    public R<Boolean> addSpecialties(@RequestBody PoliceSpecialties request) {
        return R.ok(policeSpecialtiesService.save(request));
    }

    @PostMapping("/updateSpecialties")
    @ApiOperation("更新特长")
    public R<Boolean> updateSpecialties(@RequestBody PoliceSpecialties request) {
        return R.ok(policeSpecialtiesService.updateById(request));
    }

    @PostMapping("/deleteSpecialties")
    @ApiOperation("删除特长")
    public R<Boolean> deleteSpecialties(@RequestBody PoliceSpecialties request) {
        return R.ok(policeSpecialtiesService.removeById(request));
    }


    @PostMapping("/listMarriageStatus")
    @ApiOperation(value = "婚姻状况",
            tags = "档案列表")
    public R<List<PoliceMarriageStatus>> listMarriageStatus(@RequestBody PoliceBaseQueryRequest request) {
        Page<PoliceMarriageStatus> page = policeMarriageStatusService.page(Page.of(request.getPage(), request.getLimit()),
                Wrappers.<PoliceMarriageStatus>lambdaQuery()
                        .eq(PoliceMarriageStatus::getIdCard, request.getIdCard())
                        .orderByDesc(PoliceMarriageStatus::getChangeDate));
        return R.ok(page.getRecords(), (int) page.getTotal());
    }

    @PostMapping("/addMarriageStatus")
    @ApiOperation("添加婚姻状况")
    public R<Boolean> addMarriageStatus(@RequestBody PoliceMarriageStatus request) {
        return R.ok(policeMarriageStatusService.save(request));
    }

    @PostMapping("/updateMarriageStatus")
    @ApiOperation("更新婚姻状况")
    public R<Boolean> updateMarriageStatus(@RequestBody PoliceMarriageStatus request) {
        return R.ok(policeMarriageStatusService.updateById(request));
    }

    @PostMapping("/deleteMarriageStatus")
    @ApiOperation("删除婚姻状况")
    public R<Boolean> deleteMarriageStatus(@RequestBody PoliceMarriageStatus request) {
        return R.ok(policeMarriageStatusService.removeById(request));
    }

    @PostMapping("/listPassport")
    @ApiOperation(value = "护照信息",
            tags = "档案列表")
    public R<List<PolicePassport>> listPassport(@RequestBody PoliceBaseQueryRequest request) {
        Page<PolicePassport> page = policePassportService.page(Page.of(request.getPage(), request.getLimit()),
                Wrappers.<PolicePassport>lambdaQuery()
                        .eq(PolicePassport::getIdCard, request.getIdCard())
                        .orderByDesc(PolicePassport::getIssueDate));
        return R.ok(page.getRecords(), (int) page.getTotal());
    }

    @PostMapping("/addPassport")
    @ApiOperation("添加护照信息")
    public R<Boolean> addPassport(@RequestBody PolicePassport request) {
        return R.ok(policePassportService.save(request));
    }

    @PostMapping("/updatePassport")
    @ApiOperation("更新护照信息")
    public R<Boolean> updatePassport(@RequestBody PolicePassport request) {
        return R.ok(policePassportService.updateById(request));
    }

    @PostMapping("/deletePassport")
    @ApiOperation("删除护照信息")
    public R<Boolean> deletePassport(@RequestBody PolicePassport request) {
        return R.ok(policePassportService.removeById(request));
    }


    @PostMapping("/listHkMacauTaiwanPermit")
    @ApiOperation(value = "港澳台通行证信息",
            tags = "档案列表")
    public R<List<PoliceHkMacauTaiwanPermit>> listHkMacauTaiwanPermit(@RequestBody PoliceBaseQueryRequest request) {
        Page<PoliceHkMacauTaiwanPermit> page = policeHkMacauTaiwanPermitService.page(Page.of(request.getPage(), request.getLimit()),
                Wrappers.<PoliceHkMacauTaiwanPermit>lambdaQuery()
                        .eq(PoliceHkMacauTaiwanPermit::getIdCard, request.getIdCard())
                        .orderByDesc(PoliceHkMacauTaiwanPermit::getIssueDate));
        return R.ok(page.getRecords(), (int) page.getTotal());
    }

    @PostMapping("/addHkMacauTaiwanPermit")
    @ApiOperation("添加港澳台通行证信息")
    public R<Boolean> addHkMacauTaiwanPermit(@RequestBody PoliceHkMacauTaiwanPermit request) {
        return R.ok(policeHkMacauTaiwanPermitService.save(request));
    }

    @PostMapping("/updateHkMacauTaiwanPermit")
    @ApiOperation("更新港澳台通行证信息")
    public R<Boolean> updateHkMacauTaiwanPermit(@RequestBody PoliceHkMacauTaiwanPermit request) {
        return R.ok(policeHkMacauTaiwanPermitService.updateById(request));
    }

    @PostMapping("/deleteHkMacauTaiwanPermit")
    @ApiOperation("删除港澳台通行证信息")
    public R<Boolean> deleteHkMacauTaiwanPermit(@RequestBody PoliceHkMacauTaiwanPermit request) {
        return R.ok(policeHkMacauTaiwanPermitService.removeById(request));
    }

    @PostMapping("/listHkMacauTaiwanTravel")
    @ApiOperation(value = "因私港澳台通行证信息",
            tags = "档案列表")
    public R<List<PoliceHkMacauTaiwanTravel>> listHkMacauTaiwanTravel(@RequestBody PoliceBaseQueryRequest request) {
        Page<PoliceHkMacauTaiwanTravel> page = policeHkMacauTaiwanTravelService.page(Page.of(request.getPage(), request.getLimit()),
                Wrappers.<PoliceHkMacauTaiwanTravel>lambdaQuery()
                        .eq(PoliceHkMacauTaiwanTravel::getIdCard, request.getIdCard())
                        .orderByDesc(PoliceHkMacauTaiwanTravel::getStartDate));
        return R.ok(page.getRecords(), (int) page.getTotal());
    }

    @PostMapping("/addHkMacauTaiwanTravel")
    @ApiOperation("添加因私港澳台通行证信息")
    public R<Boolean> addHkMacauTaiwanTravel(@RequestBody PoliceHkMacauTaiwanTravel request) {
        return R.ok(policeHkMacauTaiwanTravelService.save(request));
    }

    @PostMapping("/updateHkMacauTaiwanTravel")
    @ApiOperation("更新因私港澳台通行证信息")
    public R<Boolean> updateHkMacauTaiwanTravel(@RequestBody PoliceHkMacauTaiwanTravel request) {
        return R.ok(policeHkMacauTaiwanTravelService.updateById(request));
    }

    @PostMapping("/deleteHkMacauTaiwanTravel")
    @ApiOperation("删除因私港澳台通行证信息")
    public R<Boolean> deleteHkMacauTaiwanTravel(@RequestBody PoliceHkMacauTaiwanTravel request) {
        return R.ok(policeHkMacauTaiwanTravelService.removeById(request));
    }


    @PostMapping("/listLoanInfo")
    @ApiOperation(value = "个人借贷信息",
            tags = "档案列表")
    public R<List<PoliceLoanInfo>> listLoanInfo(@RequestBody PoliceBaseQueryRequest request) {
        Page<PoliceLoanInfo> page = policeLoanInfoService.page(Page.of(request.getPage(), request.getLimit()),
                Wrappers.<PoliceLoanInfo>lambdaQuery()
                        .eq(PoliceLoanInfo::getIdCard, request.getIdCard())
                        .orderByDesc(PoliceLoanInfo::getLoanDate));
        return R.ok(page.getRecords(), (int) page.getTotal());
    }

    @PostMapping("/addLoanInfo")
    @ApiOperation("添加个人借贷信息")
    public R<Boolean> addLoanInfo(@RequestBody PoliceLoanInfo request) {
        return R.ok(policeLoanInfoService.save(request));
    }

    @PostMapping("/updateLoanInfo")
    @ApiOperation("更新个人借贷信息")
    public R<Boolean> updateLoanInfo(@RequestBody PoliceLoanInfo request) {
        return R.ok(policeLoanInfoService.updateById(request));
    }

    @PostMapping("/deleteLoanInfo")
    @ApiOperation("删除个人借贷信息")
    public R<Boolean> deleteLoanInfo(@RequestBody PoliceLoanInfo request) {
        return R.ok(policeLoanInfoService.removeById(request));
    }


    @PostMapping("/listWeddingFuneralEvents")
    @ApiOperation(value = "操办婚丧嫁娶信息",
            tags = "档案列表")
    public R<List<PoliceWeddingFuneralEvents>> listWeddingFuneralEvents(@RequestBody PoliceBaseQueryRequest request) {
        Page<PoliceWeddingFuneralEvents> page = policeWeddingFuneralEventsService.page(Page.of(request.getPage(), request.getLimit()),
                Wrappers.<PoliceWeddingFuneralEvents>lambdaQuery()
                        .eq(PoliceWeddingFuneralEvents::getIdCard, request.getIdCard())
                        .orderByDesc(PoliceWeddingFuneralEvents::getEventDate));
        return R.ok(page.getRecords(), (int) page.getTotal());
    }

    @PostMapping("/addWeddingFuneralEvents")
    @ApiOperation("添加婚丧嫁娶信息")
    public R<Boolean> addWeddingFuneralEvents(@RequestBody PoliceWeddingFuneralEvents request) {
        return R.ok(policeWeddingFuneralEventsService.save(request));
    }

    @PostMapping("/updateWeddingFuneralEvents")
    @ApiOperation("更新婚丧嫁娶信息")
    public R<Boolean> updateWeddingFuneralEvents(@RequestBody PoliceWeddingFuneralEvents request) {
        return R.ok(policeWeddingFuneralEventsService.updateById(request));
    }

    @PostMapping("/deleteWeddingFuneralEvents")
    @ApiOperation("删除婚丧嫁娶信息")
    public R<Boolean> deleteWeddingFuneralEvents(@RequestBody PoliceWeddingFuneralEvents request) {
        return R.ok(policeWeddingFuneralEventsService.removeById(request));
    }


    @PostMapping("/listInvestmentInfo")
    @ApiOperation(value = "投资信息",
            tags = "档案列表")
    public R<List<PoliceInvestmentInfo>> listInvestmentInfo(@RequestBody PoliceBaseQueryRequest request) {
        Page<PoliceInvestmentInfo> page = policeInvestmentInfoService.page(Page.of(request.getPage(), request.getLimit()),
                Wrappers.<PoliceInvestmentInfo>lambdaQuery()
                        .eq(PoliceInvestmentInfo::getIdCard, request.getIdCard())
                        .orderByDesc(PoliceInvestmentInfo::getTransactionDate));
        return R.ok(page.getRecords(), (int) page.getTotal());
    }

    @PostMapping("/addInvestmentInfo")
    @ApiOperation("添加投资信息")
    public R<Boolean> addInvestmentInfo(@RequestBody PoliceInvestmentInfo request) {
        return R.ok(policeInvestmentInfoService.save(request));
    }

    @PostMapping("/updateInvestmentInfo")
    @ApiOperation("更新投资信息")
    public R<Boolean> updateInvestmentInfo(@RequestBody PoliceInvestmentInfo request) {
        return R.ok(policeInvestmentInfoService.updateById(request));
    }

    @PostMapping("/deleteInvestmentInfo")
    @ApiOperation("删除投资信息")
    public R<Boolean> deleteInvestmentInfo(@RequestBody PoliceInvestmentInfo request) {
        return R.ok(policeInvestmentInfoService.removeById(request));
    }


    @PostMapping("/listHealthStatus")
    @ApiOperation(value = "健康状况",
            tags = "档案列表")
    public R<List<PoliceHealthStatus>> listHealthStatus(@RequestBody PoliceBaseQueryRequest request) {
        Page<PoliceHealthStatus> page = policeHealthStatusService.page(Page.of(request.getPage(), request.getLimit()),
                Wrappers.<PoliceHealthStatus>lambdaQuery()
                        .eq(PoliceHealthStatus::getIdCard, request.getIdCard())
                        .orderByDesc(PoliceHealthStatus::getDiagnosisDate));
        return R.ok(page.getRecords(), (int) page.getTotal());
    }

    @PostMapping("/addHealthStatus")
    @ApiOperation("添加健康状况")
    public R<Boolean> addHealthStatus(@RequestBody PoliceHealthStatus request) {
        return R.ok(policeHealthStatusService.save(request));
    }

    @PostMapping("/updateHealthStatus")
    @ApiOperation("更新健康状况")
    public R<Boolean> updateHealthStatus(@RequestBody PoliceHealthStatus request) {
        return R.ok(policeHealthStatusService.updateById(request));
    }

    @PostMapping("/deleteHealthStatus")
    @ApiOperation("删除健康状况")
    public R<Boolean> deleteHealthStatus(@RequestBody PoliceHealthStatus request) {
        return R.ok(policeHealthStatusService.removeById(request));
    }


    @PostMapping("/listOrganizationalInquiry")
    @ApiOperation(value = "本人被组织谈话、询问、讯问等情况",
            tags = "档案列表")
    public R<List<PoliceOrganizationalInquiry>> listOrganizationalInquiry(@RequestBody PoliceBaseQueryRequest request) {
        Page<PoliceOrganizationalInquiry> page = policeOrganizationalInquiryService.page(Page.of(request.getPage(), request.getLimit()),
                Wrappers.<PoliceOrganizationalInquiry>lambdaQuery()
                        .eq(PoliceOrganizationalInquiry::getIdCard, request.getIdCard())
                        .orderByDesc(PoliceOrganizationalInquiry::getHandlingDate));
        return R.ok(page.getRecords(), (int) page.getTotal());
    }

    @PostMapping("/addOrganizationalInquiry")
    @ApiOperation("添加本人被组织谈话、询问、讯问等情况")
    public R<Boolean> addOrganizationalInquiry(@RequestBody PoliceOrganizationalInquiry request) {
        return R.ok(policeOrganizationalInquiryService.save(request));
    }

    @PostMapping("/updateOrganizationalInquiry")
    @ApiOperation("更新本人被组织谈话、询问、讯问等情况")
    public R<Boolean> updateOrganizationalInquiry(@RequestBody PoliceOrganizationalInquiry request) {
        return R.ok(policeOrganizationalInquiryService.updateById(request));
    }

    @PostMapping("/deleteOrganizationalInquiry")
    @ApiOperation("删除本人被组织谈话、询问、讯问等情况")
    public R<Boolean> deleteOrganizationalInquiry(@RequestBody PoliceOrganizationalInquiry request) {
        return R.ok(policeOrganizationalInquiryService.removeById(request));
    }


    @PostMapping("/listChildrenForeignMarriage")
    @ApiOperation(value = "子女与外国人通婚情况",
            tags = "档案列表")
    public R<List<PoliceChildrenForeignMarriage>> listChildrenForeignMarriage(@RequestBody PoliceBaseQueryRequest request) {
        Page<PoliceChildrenForeignMarriage> page = policeChildrenForeignMarriageService.page(Page.of(request.getPage(), request.getLimit()),
                Wrappers.<PoliceChildrenForeignMarriage>lambdaQuery()
                        .eq(PoliceChildrenForeignMarriage::getIdCard, request.getIdCard())
                        .orderByDesc(PoliceChildrenForeignMarriage::getRegistrationDate));
        return R.ok(page.getRecords(), (int) page.getTotal());

    }

    @PostMapping("/addChildrenForeignMarriage")
    @ApiOperation("添加子女与外国人通婚情况")
    public R<Boolean> addChildrenForeignMarriage(@RequestBody PoliceChildrenForeignMarriage request) {
        return R.ok(policeChildrenForeignMarriageService.save(request));
    }

    @PostMapping("/updateChildrenForeignMarriage")
    @ApiOperation("更新子女与外国人通婚情况")
    public R<Boolean> updateChildrenForeignMarriage(@RequestBody PoliceChildrenForeignMarriage request) {
        return R.ok(policeChildrenForeignMarriageService.updateById(request));
    }

    @PostMapping("/deleteChildrenForeignMarriage")
    @ApiOperation("删除子女与外国人通婚情况")
    public R<Boolean> deleteChildrenForeignMarriage(@RequestBody PoliceChildrenForeignMarriage request) {
        return R.ok(policeChildrenForeignMarriageService.removeById(request));
    }


    @PostMapping("/listFamilyOverseasMigration")
    @ApiOperation(value = "配偶子女移居国外情况",
            tags = "档案列表")
    public R<List<PoliceFamilyOverseasMigration>> listFamilyOverseasMigration(@RequestBody PoliceBaseQueryRequest request) {
        Page<PoliceFamilyOverseasMigration> page = policeFamilyOverseasMigrationService.page(Page.of(request.getPage(), request.getLimit()),
                Wrappers.<PoliceFamilyOverseasMigration>lambdaQuery()
                        .eq(PoliceFamilyOverseasMigration::getIdCard, request.getIdCard())
                        .orderByDesc(PoliceFamilyOverseasMigration::getBasisDate));
        return R.ok(page.getRecords(), (int) page.getTotal());
    }

    @PostMapping("/addFamilyOverseasMigration")
    @ApiOperation("添加配偶子女移居国外情况")
    public R<Boolean> addFamilyOverseasMigration(@RequestBody PoliceFamilyOverseasMigration request) {
        return R.ok(policeFamilyOverseasMigrationService.save(request));
    }

    @PostMapping("/updateFamilyOverseasMigration")
    @ApiOperation("更新配偶子女移居国外情况")
    public R<Boolean> updateFamilyOverseasMigration(@RequestBody PoliceFamilyOverseasMigration request) {
        return R.ok(policeFamilyOverseasMigrationService.updateById(request));
    }

    @PostMapping("/deleteFamilyOverseasMigration")
    @ApiOperation("删除配偶子女移居国外情况")
    public R<Boolean> deleteFamilyOverseasMigration(@RequestBody PoliceFamilyOverseasMigration request) {
        return R.ok(policeFamilyOverseasMigrationService.removeById(request));
    }


    @PostMapping("/listFamilyCriminalLiability")
    @ApiOperation(value = "配偶子女及其配偶被追究刑事责任",
            tags = "档案列表")
    public R<List<PoliceFamilyCriminalLiability>> listFamilyCriminalLiability(@RequestBody PoliceBaseQueryRequest request) {
        Page<PoliceFamilyCriminalLiability> page = policeFamilyCriminalLiabilityService.page(Page.of(request.getPage(), request.getLimit()),
                Wrappers.<PoliceFamilyCriminalLiability>lambdaQuery()
                        .eq(PoliceFamilyCriminalLiability::getIdCard, request.getIdCard())
                        .orderByDesc(PoliceFamilyCriminalLiability::getProsecutionDate));
        return R.ok(page.getRecords(), (int) page.getTotal());
    }

    @PostMapping("/addFamilyCriminalLiability")
    @ApiOperation("添加配偶子女及其配偶被追究刑事责任")
    public R<Boolean> addFamilyCriminalLiability(@RequestBody PoliceFamilyCriminalLiability request) {
        return R.ok(policeFamilyCriminalLiabilityService.save(request));
    }

    @PostMapping("/updateFamilyCriminalLiability")
    @ApiOperation("更新配偶子女及其配偶被追究刑事责任")
    public R<Boolean> updateFamilyCriminalLiability(@RequestBody PoliceFamilyCriminalLiability request) {
        return R.ok(policeFamilyCriminalLiabilityService.updateById(request));
    }

    @PostMapping("/deleteFamilyCriminalLiability")
    @ApiOperation("删除配偶子女及其配偶被追究刑事责任")
    public R<Boolean> deleteFamilyCriminalLiability(@RequestBody PoliceFamilyCriminalLiability request) {
        return R.ok(policeFamilyCriminalLiabilityService.removeById(request));
    }

    @PostMapping("/listFamilyBusiness")
    @ApiOperation(value = "配偶子女经商办企业情况",
            tags = "档案列表")
    public R<List<PoliceFamilyBusiness>> listFamilyBusiness(@RequestBody PoliceBaseQueryRequest request) {
        Page<PoliceFamilyBusiness> page = policeFamilyBusinessService.page(Page.of(request.getPage(), request.getLimit()),
                Wrappers.<PoliceFamilyBusiness>lambdaQuery()
                        .eq(PoliceFamilyBusiness::getIdCard, request.getIdCard())
                        .orderByDesc(PoliceFamilyBusiness::getEstablishmentDate));
        return R.ok(page.getRecords(), (int) page.getTotal());
    }

    @PostMapping("/addFamilyBusiness")
    @ApiOperation(value = "添加配偶子女经商办企业情况")
    public R<Boolean> addFamilyBusiness(@RequestBody PoliceFamilyBusiness request) {
        return R.ok(policeFamilyBusinessService.save(request));
    }

    @PostMapping("/updateFamilyBusiness")
    @ApiOperation("更新配偶子女经商办企业情况")
    public R<Boolean> updateFamilyBusiness(@RequestBody PoliceFamilyBusiness request) {
        return R.ok(policeFamilyBusinessService.updateById(request));
    }

    @PostMapping("/deleteFamilyBusiness")
    @ApiOperation("删除配偶子女经商办企业情况")
    public R<Boolean> deleteFamilyBusiness(@RequestBody PoliceFamilyBusiness request) {
        return R.ok(policeFamilyBusinessService.removeById(request));
    }


    @PostMapping("/listFamilyPaidInstitutions")
    @ApiOperation(value = "配偶子女开办有偿社会机构情况",
            tags = "档案列表")
    public R<List<PoliceFamilyPaidInstitutions>> listFamilyPaidInstitutions(@RequestBody PoliceBaseQueryRequest request) {
        Page<PoliceFamilyPaidInstitutions> page = policeFamilyPaidInstitutionsService.page(Page.of(request.getPage(), request.getLimit()),
                Wrappers.<PoliceFamilyPaidInstitutions>lambdaQuery()
                        .eq(PoliceFamilyPaidInstitutions::getIdCard, request.getIdCard())
                        .orderByDesc(PoliceFamilyPaidInstitutions::getEstablishmentDate));
        return R.ok(page.getRecords(), (int) page.getTotal());
    }

    @PostMapping("/addFamilyPaidInstitutions")
    @ApiOperation("添加配偶子女开办有偿社会机构情况")
    public R<Boolean> addFamilyPaidInstitutions(@RequestBody PoliceFamilyPaidInstitutions request) {
        return R.ok(policeFamilyPaidInstitutionsService.save(request));
    }

    @PostMapping("/updateFamilyPaidInstitutions")
    @ApiOperation("更新配偶子女开办有偿社会机构情况")
    public R<Boolean> updateFamilyPaidInstitutions(@RequestBody PoliceFamilyPaidInstitutions request) {
        return R.ok(policeFamilyPaidInstitutionsService.updateById(request));
    }

    @PostMapping("/deleteFamilyPaidInstitutions")
    @ApiOperation("删除配偶子女开办有偿社会机构情况")
    public R<Boolean> deleteFamilyPaidInstitutions(@RequestBody PoliceFamilyPaidInstitutions request) {
        return R.ok(policeFamilyPaidInstitutionsService.removeById(request));
    }

    @PostMapping("/listFamilyPrivateEquityFund")
    @ApiOperation(value = "配偶子女开办私募基金情况",
            tags = "档案列表")
    public R<List<PoliceFamilyPrivateEquityFund>> listFamilyPrivateEquityFund(@RequestBody PoliceBaseQueryRequest request) {
        Page<PoliceFamilyPrivateEquityFund> page = policeFamilyPrivateEquityFundService.page(Page.of(request.getPage(), request.getLimit()),
                Wrappers.<PoliceFamilyPrivateEquityFund>lambdaQuery()
                        .eq(PoliceFamilyPrivateEquityFund::getIdCard, request.getIdCard())
                        .orderByDesc(PoliceFamilyPrivateEquityFund::getContractSigningDate));
        return R.ok(page.getRecords(), (int) page.getTotal());
    }

    @PostMapping("/addFamilyPrivateEquityFund")
    @ApiOperation("添加配偶子女开办私募基金情况")
    public R<Boolean> addFamilyPrivateEquityFund(@RequestBody PoliceFamilyPrivateEquityFund request) {
        return R.ok(policeFamilyPrivateEquityFundService.save(request));
    }

    @PostMapping("/updateFamilyPrivateEquityFund")
    @ApiOperation("更新配偶子女开办私募基金情况")
    public R<Boolean> updateFamilyPrivateEquityFund(@RequestBody PoliceFamilyPrivateEquityFund request) {
        return R.ok(policeFamilyPrivateEquityFundService.updateById(request));
    }

    @PostMapping("/deleteFamilyPrivateEquityFund")
    @ApiOperation("删除配偶子女开办私募基金情况")
    public R<Boolean> deleteFamilyPrivateEquityFund(@RequestBody PoliceFamilyPrivateEquityFund request) {
        return R.ok(policeFamilyPrivateEquityFundService.removeById(request));
    }


    @PostMapping("/listFamilyRealEstate")
    @ApiOperation(value = "本人配偶子女房产情况",
            tags = "档案列表")
    public R<List<PoliceFamilyRealEstate>> listFamilyRealEstate(@RequestBody PoliceBaseQueryRequest request) {
        Page<PoliceFamilyRealEstate> page = policeFamilyRealEstateService.page(Page.of(request.getPage(), request.getLimit()),
                Wrappers.<PoliceFamilyRealEstate>lambdaQuery()
                        .eq(PoliceFamilyRealEstate::getIdCard, request.getIdCard()));
        return R.ok(page.getRecords(), (int) page.getTotal());
    }

    @PostMapping("/addFamilyRealEstate")
    @ApiOperation("本人配偶子女房产情况")
    public R<Boolean> addFamilyRealEstate(@RequestBody PoliceFamilyRealEstate request) {
        return R.ok(policeFamilyRealEstateService.save(request));
    }

    @PostMapping("/updateFamilyRealEstate")
    @ApiOperation("更新本人配偶子女房产情况")
    public R<Boolean> updateFamilyRealEstate(@RequestBody PoliceFamilyRealEstate request) {
        return R.ok(policeFamilyRealEstateService.updateById(request));
    }

    @PostMapping("/deleteFamilyRealEstate")
    @ApiOperation("删除本人配偶子女房产情况")
    public R<Boolean> deleteFamilyRealEstate(@RequestBody PoliceFamilyRealEstate request) {
        return R.ok(policeFamilyRealEstateService.removeById(request));
    }

    @PostMapping("/listFamilyVehicles")
    @ApiOperation(value = "本人配偶子女购置50万以上车辆",
            tags = "档案列表")
    public R<List<PoliceFamilyVehicles>> listFamilyVehicles(@RequestBody PoliceBaseQueryRequest request) {
        Page<PoliceFamilyVehicles> page = policeFamilyVehiclesService.page(Page.of(request.getPage(), request.getLimit()),
                Wrappers.<PoliceFamilyVehicles>lambdaQuery()
                        .eq(PoliceFamilyVehicles::getIdCard, request.getIdCard()));
        return R.ok(page.getRecords(), (int) page.getTotal());
    }

    @PostMapping("/addFamilyVehicles")
    @ApiOperation("本人配偶子女购置50万以上车辆")
    public R<Boolean> addFamilyVehicles(@RequestBody PoliceFamilyVehicles request) {
        return R.ok(policeFamilyVehiclesService.save(request));
    }

    @PostMapping("/updateFamilyVehicles")
    @ApiOperation("更新本人配偶子女购置50万以上车辆")
    public R<Boolean> updateFamilyVehicles(@RequestBody PoliceFamilyVehicles request) {
        return R.ok(policeFamilyVehiclesService.updateById(request));
    }

    @PostMapping("/deleteFamilyVehicles")
    @ApiOperation("删除本人配偶子女购置50万以上车辆")
    public R<Boolean> deleteFamilyVehicles(@RequestBody PoliceFamilyVehicles request) {
        return R.ok(policeFamilyVehiclesService.removeById(request));
    }

    @PostMapping("/listFamilySecuritiesInsurance")
    @ApiOperation(value = "本人配偶子女股票、基金、投资型保险情况",
            tags = "档案列表")
    public R<List<PoliceFamilySecuritiesInsurance>> listFamilySecuritiesInsurance(@RequestBody PoliceBaseQueryRequest request) {
        Page<PoliceFamilySecuritiesInsurance> page = policeFamilySecuritiesInsuranceService.page(Page.of(request.getPage(), request.getLimit()),
                Wrappers.<PoliceFamilySecuritiesInsurance>lambdaQuery()
                        .eq(PoliceFamilySecuritiesInsurance::getIdCard, request.getIdCard()));
        return R.ok(page.getRecords(), (int) page.getTotal());
    }

    @PostMapping("/addFamilySecuritiesInsurance")
    @ApiOperation("添加本人配偶子女股票、基金、投资型保险情况")
    public R<Boolean> addFamilySecuritiesInsurance(@RequestBody PoliceFamilySecuritiesInsurance request) {
        return R.ok(policeFamilySecuritiesInsuranceService.save(request));
    }

    @PostMapping("/updateFamilySecuritiesInsurance")
    @ApiOperation("更新本人配偶子女股票、基金、投资型保险情况")
    public R<Boolean> updateFamilySecuritiesInsurance(@RequestBody PoliceFamilySecuritiesInsurance request) {
        return R.ok(policeFamilySecuritiesInsuranceService.updateById(request));
    }

    @PostMapping("/deleteFamilySecuritiesInsurance")
    @ApiOperation("删除本人配偶子女股票、基金、投资型保险情况")
    public R<Boolean> deleteFamilySecuritiesInsurance(@RequestBody PoliceFamilySecuritiesInsurance request) {
        return R.ok(policeFamilySecuritiesInsuranceService.removeById(request));
    }


    @PostMapping("/listOtherMatters")
    @ApiOperation(value = "个人认为需要报告的其它事项",
            tags = "档案列表")
    public R<List<PoliceOtherMatters>> listOtherMatters(@RequestBody PoliceBaseQueryRequest request) {
        Page<PoliceOtherMatters> page = policeOtherMattersService.page(Page.of(request.getPage(), request.getLimit()),
                Wrappers.<PoliceOtherMatters>lambdaQuery()
                        .eq(PoliceOtherMatters::getIdCard, request.getIdCard()));
        return R.ok(page.getRecords(), (int) page.getTotal());
    }

    @PostMapping("/addOtherMatters")
    @ApiOperation("添加个人认为需要报告的其它事项")
    public R<Boolean> addOtherMatters(@RequestBody PoliceOtherMatters request) {
        return R.ok(policeOtherMattersService.save(request));
    }

    @PostMapping("/updateOtherMatters")
    @ApiOperation("更新个人认为需要报告的其它事项")
    public R<Boolean> updateOtherMatters(@RequestBody PoliceOtherMatters request) {
        return R.ok(policeOtherMattersService.updateById(request));
    }

    @PostMapping("/deleteOtherMatters")
    @ApiOperation("删除个人认为需要报告的其它事项")
    public R<Boolean> deleteOtherMatters(@RequestBody PoliceOtherMatters request) {
        return R.ok(policeOtherMattersService.removeById(request));
    }


    @PostMapping("/listDishonestExecutor")
    @ApiOperation(value = "本人或者配偶列为失信被执行人的情况",
            tags = "档案列表")
    public R<List<PoliceDishonestExecutor>> listDishonestExecutor(@RequestBody PoliceBaseQueryRequest request) {
        Page<PoliceDishonestExecutor> page = policeDishonestExecutorService.page(Page.of(request.getPage(), request.getLimit()),
                Wrappers.<PoliceDishonestExecutor>lambdaQuery()
                        .eq(PoliceDishonestExecutor::getIdCard, request.getIdCard()));
        return R.ok(page.getRecords(), (int) page.getTotal());
    }


    @PostMapping("/addDishonestExecutor")
    @ApiOperation(value = "添加本人或者配偶列为失信被执行人的情况")
    public R<Boolean> addDishonestExecutor(@RequestBody PoliceDishonestExecutor request) {
        return R.ok(policeDishonestExecutorService.save(request));
    }

    @PostMapping("/updateDishonestExecutor")
    @ApiOperation(value = "更新本人或者配偶列为失信被执行人的情况")
    public R<Boolean> updateDishonestExecutor(@RequestBody PoliceDishonestExecutor request) {
        return R.ok(policeDishonestExecutorService.updateById(request));
    }

    @PostMapping("/deleteDishonestExecutor")
    @ApiOperation("删除本人或者配偶列为失信被执行人的情况")
    public R<Boolean> deleteDishonestExecutor(@RequestBody PoliceDishonestExecutor request) {
        return R.ok(policeDishonestExecutorService.removeById(request));
    }


    @PostMapping("/listOnlineExam")
    @ApiOperation(value = "考试查询",
            tags = "档案列表")
    public R<List<PoliceOnlineExam>> listOnlineExam(@RequestBody PoliceBaseQueryRequest request) {
        Page<PoliceOnlineExam> page = policeOnlineExamService.page(Page.of(request.getPage(), request.getLimit()),
                Wrappers.<PoliceOnlineExam>lambdaQuery()
                        .eq(PoliceOnlineExam::getIdCard, request.getIdCard())
                        .orderByDesc(PoliceOnlineExam::getStartTime));
        return R.ok(page.getRecords(), (int) page.getTotal());
    }

    @PostMapping("/addOnlineExam")
    @ApiOperation("添加考试查询")
    public R<Boolean> addOnlineExam(@RequestBody PoliceOnlineExam request) {
        return R.ok(policeOnlineExamService.save(request));
    }

    @PostMapping("/updateOnlineExam")
    @ApiOperation("更新考试查询")
    public R<Boolean> updateOnlineExam(@RequestBody PoliceOnlineExam request) {
        return R.ok(policeOnlineExamService.updateById(request));
    }

    @PostMapping("/deleteOnlineExam")
    @ApiOperation("删除考试查询")
    public R<Boolean> deleteOnlineExam(@RequestBody PoliceOnlineExam request) {
        return R.ok(policeOnlineExamService.removeById(request));
    }

    @PostMapping("/listTrainingRecords")
    @ApiOperation(value = "训历明细",
            tags = "档案列表")
    public R<List<PoliceTrainingRecords>> listTrainingRecords(@RequestBody PoliceBaseQueryRequest request) {
        Page<PoliceTrainingRecords> page = policeTrainingRecordsService.page(Page.of(request.getPage(), request.getLimit()),
                Wrappers.<PoliceTrainingRecords>lambdaQuery()
                        .eq(PoliceTrainingRecords::getIdCard, request.getIdCard())
                        .orderByDesc(PoliceTrainingRecords::getTrainingTime));
        return R.ok(page.getRecords(), (int) page.getTotal());
    }

    @PostMapping("/addTrainingRecords")
    @ApiOperation("添加训历明细")
    public R<Boolean> addTrainingRecords(@RequestBody PoliceTrainingRecords request) {
        return R.ok(policeTrainingRecordsService.save(request));
    }

    @PostMapping("/updateTrainingRecords")
    @ApiOperation("更新训历明细")
    public R<Boolean> updateTrainingRecords(@RequestBody PoliceTrainingRecords request) {
        return R.ok(policeTrainingRecordsService.updateById(request));
    }

    @PostMapping("/deleteTrainingRecords")
    @ApiOperation("删除训历明细")
    public R<Boolean> deleteTrainingRecords(@RequestBody PoliceTrainingRecords request) {
        return R.ok(policeTrainingRecordsService.removeById(request));
    }


    @ApiOperation("获取职业生涯")
    @PostMapping("/getCareer")
    public R<?> getCareer(@RequestBody PoliceBaseQueryRequest request) {
        String idCard = request.getIdCard();
        List<CareerEvent> resumeEvents = policeResumeService.lambdaQuery()
                .eq(PoliceResume::getIdCard, idCard)
                .isNotNull(PoliceResume::getStartDate)
                .list()
                .stream()
                .map(r -> {
                    String description = r.getWorkUnit() + " " + r.getPosition();
                    return new CareerEvent(r.getStartDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate(),
                            r.getEndDate() != null ? r.getEndDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate() : null, description, "个人简历");
                })
                .collect(Collectors.toList());

        List<CareerEvent> politicalStatusEvents = policePoliticalStatusService.lambdaQuery()
                .eq(PolicePoliticalStatus::getIdCard, idCard)
                .isNotNull(PolicePoliticalStatus::getJoinPartyDate)
                .list()
                .stream()
                .map(p -> new CareerEvent(p.getJoinPartyDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate(),
                        null
                        , p.getPoliticalIdentity(), "政治面貌"))
                .collect(Collectors.toList());

        List<CareerEvent> positionRankEvents = policePositionRankService.lambdaQuery()
                .eq(PolicePositionRank::getIdCard, idCard)
                .isNotNull(PolicePositionRank::getCurrentPositionDate)
                .list()
                .stream()
                .map(p -> {
                    String description = p.getPositionName() + " (" + p.getPolicePositionLevel() + ")";
                    return new CareerEvent(p.getCurrentPositionDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate(),
                            null, description, "职务职级");
                })
                .collect(Collectors.toList());

        List<CareerEvent> rankInfoEvents = policeRankInfoService.lambdaQuery()
                .eq(PoliceRankInfo::getIdCard, idCard)
                .isNotNull(PoliceRankInfo::getPromotionDate)
                .list()
                .stream()
                .map(r -> new CareerEvent(r.getPromotionDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate(),
                        r.getRankEndDate() != null ? r.getRankEndDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate() : null
                        , r.getRankTitle(), "警衔信息"))
                .collect(Collectors.toList());

        List<CareerEvent> careerTimeline = Stream.of(resumeEvents, politicalStatusEvents, positionRankEvents, rankInfoEvents)
                .flatMap(List::stream)
                .sorted(Comparator.comparing(CareerEvent::getStartDate).reversed())
                .collect(Collectors.toList());

        return R.ok(careerTimeline);
    }

    @PostMapping("/statisticsByDepartment")
    @ApiOperation("按部门统计民警人数、年龄段、学历、平均年龄")
    public R<List<PoliceStatisticsDTO>> statisticsByDepartment() {
        return R.ok(policeBasicInfoService.getPoliceStatisticsByDepartment());
    }

    @PostMapping("/statisticsPoliceByOrgId")
    @ApiOperation("按部门统计民警人数、年龄段、学历、平均年龄")
    public R<PoliceStatisticsDTO> statisticsPoliceByOrgId(@RequestBody StatisticsQueryRequest request) {
        return R.ok(policeBasicInfoService.getPoliceStatisticsByOrgId(request));
    }

    @PostMapping("/statisticsDrillDown")
    @ApiOperation("统计数字穿透查询 - 根据统计类型查询人员基本信息列表")
    public R<List<PoliceBasicInfo>> statisticsDrillDown(@RequestBody StatisticsDrillDownRequest request) {
        Page<PoliceBasicInfo> page = policeBasicInfoService.getPoliceListByStatisticsType(request);
        return R.ok(page.getRecords(), (int) page.getTotal());
    }

    @PostMapping("/countHonors")
    @ApiOperation("统计表彰奖励")
    public R<Integer> countHonors(@RequestBody PoliceBaseQueryRequest request) {
        long count = policeHonorsService.count(Wrappers.<PoliceHonors>lambdaQuery()
                .eq(PoliceHonors::getIdCard, request.getIdCard()));
        return R.ok((int) count);
    }

    @PostMapping("/countWj")
    @ApiOperation("统计违纪")
    public R<Integer> countWj(@RequestBody PoliceBaseQueryRequest request) {
        long count = policeOrganizationalInquiryService.count(Wrappers.<PoliceOrganizationalInquiry>lambdaQuery()
                .eq(PoliceOrganizationalInquiry::getIdCard, request.getIdCard()));
        return R.ok((int) count);
    }


    @GetMapping("/exportBasicInfoWord")
    @ApiOperation("导出基础信息Word")
    public void exportBasicInfoWord(HttpServletResponse response,
                                    @RequestParam String idCard) throws IOException {
        PoliceBasicInfo one = policeBasicInfoService.getOne(Wrappers.<PoliceBasicInfo>lambdaQuery().eq(PoliceBasicInfo::getIdCard, idCard));

        Map<String, Object> data = new HashMap<>();
        data.put("name", one.getName());
        data.put("birth_date", DateUtil.date(one.getBirthDate()).toDateStr());
        data.put("gender", one.getGender() == 1 ? "男" : "女");
        data.put("nation", one.getNation());
        data.put("native_place", one.getNativePlace());

        data.put("img", Pictures.of(one.getImgUrl()).size(130, 200).create());
        data.put("police_work_start_date", DateUtil.date(one.getPoliceWorkStartDate()).toDateStr());
        data.put("work_start_date", DateUtil.date(one.getWorkStartDate()).toDateStr());
        data.put("leadership_level", one.getLeadershipLevel());
        data.put("home_address", one.getHomeAddress());

        LoopRowTableRenderPolicy policy = new LoopRowTableRenderPolicy();
        Configure config = Configure.builder()
                .bind("family", policy)
                .build();
        // 家人信息

        List<PoliceFamilyMembers> familyMembers = policeFamilyMembersService.list(Wrappers.<PoliceFamilyMembers>lambdaQuery()
                .eq(PoliceFamilyMembers::getIdCard, one.getIdCard()));
        if (!familyMembers.isEmpty()) {
            for (PoliceFamilyMembers familyMember : familyMembers) {
                if (StrUtil.isNotBlank(familyMember.getBirthDate())) {
                    try {
                        DateTime birthDate = DateUtil.parse(familyMember.getBirthDate() + "-01", "yyyy-MM-dd");
                        int i = DateUtil.ageOfNow(birthDate);
                        familyMember.setBirthDate(String.valueOf(i));
                    } catch (Exception e) {
                        log.error("年龄计算失败: {}", familyMember.getBirthDate());
                    }
                }
            }
        }
        data.put("family", familyMembers);

        // 简历信息
        List<PoliceResume> resumeList = policeResumeService.list(Wrappers.<PoliceResume>lambdaQuery()
                .eq(PoliceResume::getIdCard, one.getIdCard())
                .orderByAsc(PoliceResume::getStartDate));
        List<Map<String, Object>> resumeMapList = resumeList.stream().map(r -> {
            Map<String, Object> map = new HashMap<>();
            map.put("work_unit", r.getWorkUnit());
            map.put("position", r.getPosition());
            map.put("start_date", r.getStartDate() == null ? "" : DateUtil.date(r.getStartDate()).toDateStr());
            map.put("end_date", r.getEndDate() == null ? "" : DateUtil.date(r.getEndDate()).toDateStr());
            return map;
        }).collect(Collectors.toList());

        data.put("resume", resumeMapList);

        // 表彰奖励
        List<PoliceHonors> honorsList = policeHonorsService.list(Wrappers.<PoliceHonors>lambdaQuery()
                .eq(PoliceHonors::getIdCard, one.getIdCard())
                .orderByAsc(PoliceHonors::getAwardDate));
        List<Map<String, Object>> honorsMapList = honorsList.stream().map(r -> {
            Map<String, Object> map = new HashMap<>();
            map.put("honor_name", r.getHonorName());
            map.put("award_date", r.getAwardDate() == null ? "" : DateUtil.date(r.getAwardDate()).toDateStr());
            return map;
        }).collect(Collectors.toList());
        data.put("honors", honorsMapList);

        // 年度考核
        List<PoliceAnnualAssessment> annualAssessmentList = policeAnnualAssessmentService.list(Wrappers.<PoliceAnnualAssessment>lambdaQuery()
                .eq(PoliceAnnualAssessment::getIdCard, one.getIdCard())
                .orderByAsc(PoliceAnnualAssessment::getAssessmentYear));
        List<Map<String, Object>> annualAssessmentMapList = annualAssessmentList.stream().map(r -> {
            Map<String, Object> map = new HashMap<>();
            map.put("assessment_year", r.getAssessmentYear());
            map.put("assessment_result", r.getAssessmentResult());
            return map;
        }).collect(Collectors.toList());
        data.put("annual_assessment", annualAssessmentMapList);

        // 教育信息
        PoliceEducation educationList = policeEducationService.getOne(Wrappers.<PoliceEducation>lambdaQuery()
                .eq(PoliceEducation::getIdCard, one.getIdCard())
                .orderByAsc(PoliceEducation::getEnrollmentDate)
                .last(" limit 1"));
        data.put("school_name", educationList == null ? null : educationList.getSchoolName());
        data.put("major_name", educationList == null ? null : educationList.getMajorName());


        // 入党时间
        PolicePoliticalStatus politicalStatus = policePoliticalStatusService.getOne(Wrappers.<PolicePoliticalStatus>lambdaQuery()
                .eq(PolicePoliticalStatus::getIdCard, one.getIdCard())
                .in(PolicePoliticalStatus::getPoliticalIdentity, "中共党员", "中共预备党员")
                .orderByDesc(PolicePoliticalStatus::getJoinPartyDate)
                .last(" limit 1"));
        data.put("join_party_date", politicalStatus == null ? null : DateUtil.date(politicalStatus.getJoinPartyDate()).toDateStr());

        // 图片是 180 * 290
        XWPFTemplate template = XWPFTemplate.compile("conf/template/police_basic_info.docx", config)
                .render(data);
        response.setContentType("application/octet-stream");
        String fileName = URLEncoder.encode("基本信息", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".docx");
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        OutputStream out = response.getOutputStream();
        BufferedOutputStream bos = new BufferedOutputStream(out);
        template.write(bos);
        bos.flush();
        out.flush();
        PoitlIOUtils.closeQuietlyMulti(template, bos, out);
    }




}
