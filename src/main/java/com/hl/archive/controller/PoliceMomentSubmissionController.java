package com.hl.archive.controller;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.entity.PoliceMomentSubmission;
import com.hl.archive.domain.entity.PoliceMomentSubmissionVideo;
import com.hl.archive.domain.request.PoliceBaseQueryRequest;
import com.hl.archive.service.PoliceMomentSubmissionService;
import com.hl.archive.service.PoliceMomentSubmissionVideoService;
import com.hl.archive.utils.SsoCacheUtil;
import com.hl.common.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/momentSubmission")
@RequiredArgsConstructor
@Api(tags = "警彩瞬间")
public class PoliceMomentSubmissionController {

    private final PoliceMomentSubmissionService policeMomentSubmissionService;

    private final PoliceMomentSubmissionVideoService policeMomentSubmissionVideoService;



    
    @PostMapping("/queryMomentSubmission")
    @ApiOperation("查询警彩瞬间")
    public R<List<PoliceMomentSubmission>> queryMomentSubmission(@RequestBody PoliceBaseQueryRequest request) {
        String idCard = request.getIdCard();
        JSONObject userInfo = JSONObject.from(SsoCacheUtil.getUserObjByIdCard(idCard));

        String name = userInfo.getByPath("name").toString();
        String organization = userInfo.getByPath("organization[0].organization_name").toString();

        organization = organization.replaceAll("派出所", "")
                .replaceAll("武进分局", "");

        Page<PoliceMomentSubmission> page = policeMomentSubmissionService.page(Page.of(request.getPage(), request.getLimit()), Wrappers.<PoliceMomentSubmission>lambdaQuery()
                .like(PoliceMomentSubmission::getOfficerName, name)
                .like(PoliceMomentSubmission::getSubmitUnit, organization));
        if (page.getRecords().isEmpty()){
            return R.ok(page.getRecords(), (int) page.getTotal());
        }

        List<String> zjList = page.getRecords().stream().map(PoliceMomentSubmission::getZjbh).collect(Collectors.toList());
        List<PoliceMomentSubmissionVideo> list = policeMomentSubmissionVideoService.list(Wrappers.<PoliceMomentSubmissionVideo>lambdaQuery()
                .in(PoliceMomentSubmissionVideo::getSbZjbh, zjList));
        Map<String, List<PoliceMomentSubmissionVideo>> collect = list.stream().collect(Collectors.groupingBy(PoliceMomentSubmissionVideo::getSbZjbh));
        for (PoliceMomentSubmission record : page.getRecords()) {
            record.setFileList(collect.get(record.getZjbh()));
        }
        
        return R.ok(page.getRecords(), (int) page.getTotal());
    }
    
    

    
}
