package com.hl.orasync.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hl.archive.domain.entity.PolicePoliticalStatus;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMapping;
import lombok.Data;


/**
 * 政治面貌
 */
@Data
@TableName(value = "ZTJC.V_WJ_RYZZMM")
@AutoMapper(target = PolicePoliticalStatus.class,uses = {ConversionUtils.class})
public class VWjRyzzmm {
    @TableField(value = "ZZSF")
    @AutoMapping(target = "politicalIdentity")
    private String zzsf;

    @TableField(value = "CJDPSJ")
    @AutoMapping(target = "joinPartyDate",qualifiedByName = "strToDate")
    private String cjdpsj;

    @TableField(value = "RKBM")
    private String rkbm;

    @TableField(value = "GMSFHM")
    @AutoMapping(target = "idCard")
    private String gmsfhm;
}