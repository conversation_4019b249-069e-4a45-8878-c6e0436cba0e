package com.hl.orasync.sync.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.entity.PoliceMonthlyAssessment;
import com.hl.archive.service.PoliceMonthlyAssessmentService;
import com.hl.common.config.datasource.DataSourceType;
import com.hl.common.config.datasource.DynamicDataSourceContextHolder;
import com.hl.orasync.domain.VWjRyjdkh;
import com.hl.orasync.domain.VWjRyydkh;
import com.hl.orasync.service.VWjRyydkhService;
import com.hl.orasync.sync.DataSyncProcessor;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.function.Function;

@Component
@RequiredArgsConstructor
public class PoliceMonthlyAssessmentSyncProcessor implements DataSyncProcessor<VWjRyydkh, PoliceMonthlyAssessment> {


    private final VWjRyydkhService vWjRyydkhService;

    private final PoliceMonthlyAssessmentService policeMonthlyAssessmentService;


    private final Converter converter;

    @Override
    public List<VWjRyydkh> getSourceData(int offset, int limit) {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        Page<VWjRyydkh> page = vWjRyydkhService.page(Page.of(offset, limit), Wrappers.<VWjRyydkh>lambdaQuery()
                .orderByDesc(VWjRyydkh::getGmsfhm));
        DynamicDataSourceContextHolder.clearDataSourceType();
        return page.getRecords();
    }

    @Override
    public List<PoliceMonthlyAssessment> getTargetData(int offset, int limit) {
        Page<PoliceMonthlyAssessment> page = policeMonthlyAssessmentService.page(Page.of(offset, limit));
        return page.getRecords();
    }

    @Override
    public PoliceMonthlyAssessment convert(VWjRyydkh source) {
        return converter.convert(source, PoliceMonthlyAssessment.class);
    }

    @Override
    public Function<PoliceMonthlyAssessment, String> getBusinessKeyGenerator() {
        return policeMonthlyAssessment ->
                policeMonthlyAssessment.getIdCard() + "_" +
                        policeMonthlyAssessment.getAssessmentYear() + "_" +
                        policeMonthlyAssessment.getAssessmentNotice() + "_" +
                        policeMonthlyAssessment.getAssessmentScore() + "_" +
                        policeMonthlyAssessment.getAssessmentMonth() + "_" +
                        policeMonthlyAssessment.getAssessmentScore() + "_" +
                        policeMonthlyAssessment.getAssessmentBonus();
    }

    @Override
    public void batchInsert(List<PoliceMonthlyAssessment> records) {
        policeMonthlyAssessmentService.saveBatch(records);
    }

    @Override
    public void batchUpdate(List<PoliceMonthlyAssessment> records) {
        policeMonthlyAssessmentService.updateBatchById(records);
    }

    @Override
    public void batchDelete(List<String> businessKeys) {
        policeMonthlyAssessmentService.removeByIds(businessKeys);
    }

    @Override
    public long getSourceDataCount() {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        long count = vWjRyydkhService.count();
        DynamicDataSourceContextHolder.clearDataSourceType();
        return count;
    }

    @Override
    public long getTargetDataCount() {
        return policeMonthlyAssessmentService.count();
    }
}
