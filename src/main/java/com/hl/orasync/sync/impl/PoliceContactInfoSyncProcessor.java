package com.hl.orasync.sync.impl;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.entity.PoliceContactInfo;
import com.hl.archive.service.PoliceContactInfoService;
import com.hl.common.config.datasource.DataSourceType;
import com.hl.common.config.datasource.DynamicDataSourceContextHolder;
import com.hl.orasync.domain.VWjRyjtzz;
import com.hl.orasync.service.VWjRyjtzzService;
import com.hl.orasync.sync.DataSyncProcessor;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.function.Function;

@Component
@RequiredArgsConstructor
public class PoliceContactInfoSyncProcessor implements DataSyncProcessor<VWjRyjtzz, PoliceContactInfo> {

    private final Converter converter;

    private final VWjRyjtzzService vwjRyjtzzService;

    private final PoliceContactInfoService policeContactInfoService;

    @Override
    public List<VWjRyjtzz> getSourceData(int offset, int limit) {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        Page<VWjRyjtzz> page = vwjRyjtzzService.page(Page.of(offset, limit), Wrappers.<VWjRyjtzz>lambdaQuery()
                .orderByDesc(VWjRyjtzz::getGmsfhm));
        DynamicDataSourceContextHolder.clearDataSourceType();
        return page.getRecords();
    }

    @Override
    public List<PoliceContactInfo> getTargetData(int offset, int limit) {
        Page<PoliceContactInfo> page = policeContactInfoService.page(Page.of(offset, limit));
        return page.getRecords();
    }

    @Override
    public PoliceContactInfo convert(VWjRyjtzz source) {
        return converter.convert(source, PoliceContactInfo.class);
    }

    @Override
    public Function<PoliceContactInfo, String> getBusinessKeyGenerator() {
        return policeContactInfo -> policeContactInfo.getIdCard() + "_" + policeContactInfo.getMobilePhone() + "_" + policeContactInfo.getHomeAddress();
    }

    @Override
    public void batchInsert(List<PoliceContactInfo> records) {
        policeContactInfoService.saveBatch(records);
    }

    @Override
    public void batchUpdate(List<PoliceContactInfo> records) {
        policeContactInfoService.updateBatchById(records);
    }

    @Override
    public void batchDelete(List<String> businessKeys) {

    }

    @Override
    public long getSourceDataCount() {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        long count = vwjRyjtzzService.count();
        DynamicDataSourceContextHolder.clearDataSourceType();
        return count;
    }

    @Override
    public long getTargetDataCount() {
        return policeContactInfoService.count();
    }
}
