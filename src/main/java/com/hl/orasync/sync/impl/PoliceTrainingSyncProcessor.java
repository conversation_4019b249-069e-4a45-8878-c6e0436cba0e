package com.hl.orasync.sync.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.entity.PoliceTraining;
import com.hl.archive.service.PoliceTrainingService;
import com.hl.common.config.datasource.DataSourceType;
import com.hl.common.config.datasource.DynamicDataSourceContextHolder;
import com.hl.orasync.domain.VWjRyjyxl;
import com.hl.orasync.service.VWjRyjyxlService;
import com.hl.orasync.sync.DataSyncProcessor;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.function.Function;

@Component
@RequiredArgsConstructor
public class PoliceTrainingSyncProcessor implements DataSyncProcessor<VWjRyjyxl, PoliceTraining> {

    private final Converter converter;

    private final VWjRyjyxlService vWjRyjyxlService;

    private final PoliceTrainingService policeTrainingService;

    @Override
    public List<VWjRyjyxl> getSourceData(int offset, int limit) {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));

        Page<VWjRyjyxl> page = vWjRyjyxlService.page(Page.of(offset, limit), Wrappers.<VWjRyjyxl>lambdaQuery()
                .orderByDesc(VWjRyjyxl::getGmsfhm));
        DynamicDataSourceContextHolder.clearDataSourceType();

        return page.getRecords();
    }

    @Override
    public List<PoliceTraining> getTargetData(int offset, int limit) {
        Page<PoliceTraining> page = policeTrainingService.page(Page.of(offset, limit));
        return page.getRecords();
    }

    @Override
    public PoliceTraining convert(VWjRyjyxl source) {
        return converter.convert(source, PoliceTraining.class);
    }

    @Override
    public Function<PoliceTraining, String> getBusinessKeyGenerator() {
        return policeTraining ->
                policeTraining.getIdCard()+"_"+policeTraining.getTrainingName()+"_"+policeTraining.getTrainingStartDate();
    }

    @Override
    public void batchInsert(List<PoliceTraining> records) {
        policeTrainingService.saveBatch(records);
    }

    @Override
    public void batchUpdate(List<PoliceTraining> records) {
        policeTrainingService.updateBatchById(records);
    }

    @Override
    public void batchDelete(List<String> businessKeys) {


    }

    @Override
    public long getSourceDataCount() {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        long count = vWjRyjyxlService.count();
        DynamicDataSourceContextHolder.clearDataSourceType();
        return count;
    }

    @Override
    public long getTargetDataCount() {
        return policeTrainingService.count();
    }
}
