package com.hl.orasync.sync.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.entity.PolicePoliticalStatus;
import com.hl.archive.service.PolicePoliticalStatusService;
import com.hl.common.config.datasource.DataSourceType;
import com.hl.common.config.datasource.DynamicDataSourceContextHolder;
import com.hl.orasync.domain.VWjRyzzmm;
import com.hl.orasync.service.VWjRyzzmmService;
import com.hl.orasync.sync.DataSyncProcessor;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.function.Function;

@Component
@RequiredArgsConstructor
public class PolicePoliticalStatusSyncProcessor implements DataSyncProcessor<VWjRyzzmm, PolicePoliticalStatus> {

    private final VWjRyzzmmService vwjRyzzmmService;

    private final PolicePoliticalStatusService policePoliticalStatusService;

    private final Converter converter;

    @Override
    public List<VWjRyzzmm> getSourceData(int offset, int limit) {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        Page<VWjRyzzmm> page = vwjRyzzmmService.page(Page.of(offset, limit), Wrappers.<VWjRyzzmm>lambdaQuery()
                .orderByDesc(VWjRyzzmm::getGmsfhm));
        DynamicDataSourceContextHolder.clearDataSourceType();
        return page.getRecords();
    }

    @Override
    public List<PolicePoliticalStatus> getTargetData(int offset, int limit) {
        Page<PolicePoliticalStatus> page = policePoliticalStatusService.page(Page.of(offset, limit));
        return page.getRecords();
    }

    @Override
    public PolicePoliticalStatus convert(VWjRyzzmm source) {
        return converter.convert(source, PolicePoliticalStatus.class);
    }

    @Override
    public Function<PolicePoliticalStatus, String> getBusinessKeyGenerator() {
        return policePoliticalStatus ->
                policePoliticalStatus.getIdCard() + "_" + policePoliticalStatus.getPoliticalIdentity() + "_" + policePoliticalStatus.getJoinPartyDate();
    }

    @Override
    public void batchInsert(List<PolicePoliticalStatus> records) {
        policePoliticalStatusService.saveBatch(records);
    }

    @Override
    public void batchUpdate(List<PolicePoliticalStatus> records) {
        policePoliticalStatusService.updateBatchById(records);
    }

    @Override
    public void batchDelete(List<String> businessKeys) {
        policePoliticalStatusService.removeByIds(businessKeys);
    }

    @Override
    public long getSourceDataCount() {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        long count = vwjRyzzmmService.count();
        DynamicDataSourceContextHolder.clearDataSourceType();
        return count;
    }

    @Override
    public long getTargetDataCount() {
        return policePoliticalStatusService.count();
    }
}
