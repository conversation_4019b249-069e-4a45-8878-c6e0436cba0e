package com.hl.orasync.sync.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.entity.PoliceOnlineExam;
import com.hl.archive.service.PoliceOnlineExamService;
import com.hl.common.config.datasource.DataSourceType;
import com.hl.common.config.datasource.DynamicDataSourceContextHolder;
import com.hl.orasync.domain.VWjWsks;
import com.hl.orasync.service.VWjWsksService;
import com.hl.orasync.sync.DataSyncProcessor;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.function.Function;

@Component
@RequiredArgsConstructor
public class PoliceOnlineExamSyncProcessor implements DataSyncProcessor<VWjWsks, PoliceOnlineExam> {

    private final VWjWsksService vWjWsksService;

    private final PoliceOnlineExamService policeOnlineExamService;

    private final Converter converter;

    @Override
    public List<VWjWsks> getSourceData(int offset, int limit) {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        Page<VWjWsks> page = vWjWsksService.page(Page.of(offset, limit), Wrappers.<VWjWsks>lambdaQuery()
                .orderByDesc(VWjWsks::getGmsfhm)
                .orderByDesc(VWjWsks::getKscs));
        DynamicDataSourceContextHolder.clearDataSourceType();
        return page.getRecords();
    }

    @Override
    public List<PoliceOnlineExam> getTargetData(int offset, int limit) {
        Page<PoliceOnlineExam> page = policeOnlineExamService.page(Page.of(offset, limit));
        return page.getRecords();
    }

    @Override
    public PoliceOnlineExam convert(VWjWsks source) {
        return converter.convert(source, PoliceOnlineExam.class);
    }

    @Override
    public Function<PoliceOnlineExam, String> getBusinessKeyGenerator() {
        return policeOnlineExam ->
                policeOnlineExam.getIdCard() + "_" +
                        policeOnlineExam.getExamPaperName() + "_" +
                        policeOnlineExam.getStartTime() + "_" +
                        policeOnlineExam.getSubmitStatus() + "_" +
                        policeOnlineExam.getEndTime();
    }

    @Override
    public void batchInsert(List<PoliceOnlineExam> records) {
        policeOnlineExamService.saveBatch(records);
    }

    @Override
    public void batchUpdate(List<PoliceOnlineExam> records) {
        policeOnlineExamService.updateBatchById(records);
    }

    @Override
    public void batchDelete(List<String> businessKeys) {
        // 根据业务主键删除记录
        // 这里需要根据业务主键查询对应的记录ID，然后删除
        // 由于业务主键是组合键，需要解析后查询
    }

    @Override
    public long getSourceDataCount() {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        long count = vWjWsksService.count();
        DynamicDataSourceContextHolder.clearDataSourceType();
        return count;
    }

    @Override
    public long getTargetDataCount() {
        return policeOnlineExamService.count();
    }
}
