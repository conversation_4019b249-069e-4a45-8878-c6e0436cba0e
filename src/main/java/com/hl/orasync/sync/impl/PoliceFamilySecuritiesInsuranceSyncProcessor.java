package com.hl.orasync.sync.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.entity.PoliceFamilySecuritiesInsurance;
import com.hl.archive.service.PoliceFamilySecuritiesInsuranceService;
import com.hl.common.config.datasource.DataSourceType;
import com.hl.common.config.datasource.DynamicDataSourceContextHolder;
import com.hl.orasync.domain.VWjQtGpjjtz;
import com.hl.orasync.service.VWjQtGpjjtzService;
import com.hl.orasync.sync.DataSyncProcessor;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.function.Function;

@Component
@RequiredArgsConstructor
public class PoliceFamilySecuritiesInsuranceSyncProcessor implements DataSyncProcessor<VWjQtGpjjtz, PoliceFamilySecuritiesInsurance> {

    private final VWjQtGpjjtzService vWjQtGpjjtzService;

    private final PoliceFamilySecuritiesInsuranceService policeFamilySecuritiesInsuranceService;

    private final Converter converter;

    @Override
    public List<VWjQtGpjjtz> getSourceData(int offset, int limit) {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        Page<VWjQtGpjjtz> page = vWjQtGpjjtzService.page(Page.of(offset, limit), Wrappers.<VWjQtGpjjtz>lambdaQuery()
                .orderByDesc(VWjQtGpjjtz::getGmsfhm));
        DynamicDataSourceContextHolder.clearDataSourceType();
        return page.getRecords();
    }

    @Override
    public List<PoliceFamilySecuritiesInsurance> getTargetData(int offset, int limit) {
        Page<PoliceFamilySecuritiesInsurance> page = policeFamilySecuritiesInsuranceService.page(Page.of(offset, limit));
        return page.getRecords();
    }

    @Override
    public PoliceFamilySecuritiesInsurance convert(VWjQtGpjjtz source) {
        return converter.convert(source, PoliceFamilySecuritiesInsurance.class);
    }

    @Override
    public Function<PoliceFamilySecuritiesInsurance, String> getBusinessKeyGenerator() {
        return policeFamilySecuritiesInsurance ->
                policeFamilySecuritiesInsurance.getIdCard() + "_" +
                        policeFamilySecuritiesInsurance.getHolderName() + "_" +
                        policeFamilySecuritiesInsurance.getSecurityNameCode() + "_" +
                        policeFamilySecuritiesInsurance.getNetValuePremium();
    }

    @Override
    public void batchInsert(List<PoliceFamilySecuritiesInsurance> records) {
        policeFamilySecuritiesInsuranceService.saveBatch(records);
    }

    @Override
    public void batchUpdate(List<PoliceFamilySecuritiesInsurance> records) {
        policeFamilySecuritiesInsuranceService.updateBatchById(records);
    }

    @Override
    public void batchDelete(List<String> businessKeys) {
        // 根据业务主键删除记录
        // 这里需要根据业务主键查询对应的记录ID，然后删除
        // 由于业务主键是组合键，需要解析后查询
    }

    @Override
    public long getSourceDataCount() {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        long count = vWjQtGpjjtzService.count();
        DynamicDataSourceContextHolder.clearDataSourceType();
        return count;
    }

    @Override
    public long getTargetDataCount() {
        return policeFamilySecuritiesInsuranceService.count();
    }
}
