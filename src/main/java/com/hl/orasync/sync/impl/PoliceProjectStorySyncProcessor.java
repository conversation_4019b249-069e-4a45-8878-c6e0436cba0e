package com.hl.orasync.sync.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.entity.PoliceProjectStory;
import com.hl.archive.service.PoliceProjectStoryService;
import com.hl.common.config.datasource.DataSourceType;
import com.hl.common.config.datasource.DynamicDataSourceContextHolder;
import com.hl.orasync.domain.VWjXhjhRxgrTwzl;
import com.hl.orasync.domain.VWjXhjhRxgrXjsj;
import com.hl.orasync.service.VWjXhjhRxgrTwzlService;
import com.hl.orasync.service.VWjXhjhRxgrXjsjService;
import com.hl.orasync.sync.DataSyncProcessor;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.function.Function;

@Component
@RequiredArgsConstructor
public class PoliceProjectStorySyncProcessor implements DataSyncProcessor<VWjXhjhRxgrXjsj, PoliceProjectStory> {

    private final VWjXhjhRxgrXjsjService vWjXhjhRxgrXjsjService;

    private final PoliceProjectStoryService policeProjectStoryService;

    private final Converter converter;

    @Override
    public List<VWjXhjhRxgrXjsj> getSourceData(int offset, int limit) {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        Page<VWjXhjhRxgrXjsj> page = vWjXhjhRxgrXjsjService.page(Page.of(offset, limit),
                Wrappers.<VWjXhjhRxgrXjsj>lambdaQuery()
                        .orderByDesc(VWjXhjhRxgrXjsj::getXxzjbh));
        DynamicDataSourceContextHolder.clearDataSourceType();
        return page.getRecords();
    }

    @Override
    public List<PoliceProjectStory> getTargetData(int offset, int limit) {
        Page<PoliceProjectStory> page = policeProjectStoryService.page(Page.of(offset, limit));
        return page.getRecords();
    }

    @Override
    public PoliceProjectStory convert(VWjXhjhRxgrXjsj source) {
        return converter.convert(source, PoliceProjectStory.class);
    }

    @Override
    public Function<PoliceProjectStory, String> getBusinessKeyGenerator() {
        return PoliceProjectStory::getZjbh;
    }

    @Override
    public void batchInsert(List<PoliceProjectStory> records) {
        policeProjectStoryService.saveBatch(records);
    }

    @Override
    public void batchUpdate(List<PoliceProjectStory> records) {
        policeProjectStoryService.updateBatchById(records);
    }

    @Override
    public void batchDelete(List<String> businessKeys) {
        // 根据业务键删除，这里需要根据实际情况实现
        // 由于业务键是组合的，可能需要特殊处理
    }

    @Override
    public long getSourceDataCount() {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        long count = vWjXhjhRxgrXjsjService.count();
        DynamicDataSourceContextHolder.clearDataSourceType();
        return count;
    }

    @Override
    public long getTargetDataCount() {
        return policeProjectStoryService.count();
    }
}
