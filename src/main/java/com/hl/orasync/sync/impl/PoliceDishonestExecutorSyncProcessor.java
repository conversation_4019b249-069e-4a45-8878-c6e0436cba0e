package com.hl.orasync.sync.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.entity.PoliceDishonestExecutor;
import com.hl.archive.service.PoliceDishonestExecutorService;
import com.hl.common.config.datasource.DataSourceType;
import com.hl.common.config.datasource.DynamicDataSourceContextHolder;
import com.hl.orasync.domain.VWjQtPosxbzxr;
import com.hl.orasync.service.VWjQtPosxbzxrService;
import com.hl.orasync.sync.DataSyncProcessor;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.function.Function;

@Component
@RequiredArgsConstructor
public class PoliceDishonestExecutorSyncProcessor implements DataSyncProcessor<VWjQtPosxbzxr, PoliceDishonestExecutor> {

    private final VWjQtPosxbzxrService vWjQtPosxbzxrService;

    private final PoliceDishonestExecutorService policeDishonestExecutorService;

    private final Converter converter;

    @Override
    public List<VWjQtPosxbzxr> getSourceData(int offset, int limit) {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        Page<VWjQtPosxbzxr> page = vWjQtPosxbzxrService.page(Page.of(offset, limit), Wrappers.<VWjQtPosxbzxr>lambdaQuery()
                .orderByDesc(VWjQtPosxbzxr::getGmsfhm));
        DynamicDataSourceContextHolder.clearDataSourceType();
        return page.getRecords();
    }

    @Override
    public List<PoliceDishonestExecutor> getTargetData(int offset, int limit) {
        Page<PoliceDishonestExecutor> page = policeDishonestExecutorService.page(Page.of(offset, limit));
        return page.getRecords();
    }

    @Override
    public PoliceDishonestExecutor convert(VWjQtPosxbzxr source) {
        return converter.convert(source, PoliceDishonestExecutor.class);
    }

    @Override
    public Function<PoliceDishonestExecutor, String> getBusinessKeyGenerator() {
        return policeDishonestExecutor ->
                policeDishonestExecutor.getIdCard() + "_" +
                        policeDishonestExecutor.getPartyName() + "_" +
                        policeDishonestExecutor.getRelationship() + "_" +
                        policeDishonestExecutor.getDishonestDate();
    }

    @Override
    public void batchInsert(List<PoliceDishonestExecutor> records) {
        policeDishonestExecutorService.saveBatch(records);
    }

    @Override
    public void batchUpdate(List<PoliceDishonestExecutor> records) {
        policeDishonestExecutorService.updateBatchById(records);
    }

    @Override
    public void batchDelete(List<String> businessKeys) {
        // 根据业务主键删除记录
        // 这里需要根据业务主键查询对应的记录ID，然后删除
        // 由于业务主键是组合键，需要解析后查询
    }

    @Override
    public long getSourceDataCount() {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        long count = vWjQtPosxbzxrService.count();
        DynamicDataSourceContextHolder.clearDataSourceType();
        return count;
    }

    @Override
    public long getTargetDataCount() {
        return policeDishonestExecutorService.count();
    }
}
