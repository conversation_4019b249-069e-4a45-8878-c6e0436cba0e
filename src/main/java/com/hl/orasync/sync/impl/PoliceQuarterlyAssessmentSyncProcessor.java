package com.hl.orasync.sync.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.entity.PoliceQuarterlyAssessment;
import com.hl.archive.service.PoliceQuarterlyAssessmentService;
import com.hl.common.config.datasource.DataSourceType;
import com.hl.common.config.datasource.DynamicDataSourceContextHolder;
import com.hl.orasync.domain.VWjRyjdkh;
import com.hl.orasync.service.VWjRyjdkhService;
import com.hl.orasync.sync.DataSyncProcessor;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.function.Function;

@Component
@RequiredArgsConstructor
public class PoliceQuarterlyAssessmentSyncProcessor implements DataSyncProcessor<VWjRyjdkh, PoliceQuarterlyAssessment> {


    private final Converter converter;

    private final PoliceQuarterlyAssessmentService policeQuarterlyAssessmentService;

    private final VWjRyjdkhService vwjRyjdkhService;

    @Override
    public List<VWjRyjdkh> getSourceData(int offset, int limit) {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));

        Page<VWjRyjdkh> page = vwjRyjdkhService.page(Page.of(offset, limit), Wrappers.<VWjRyjdkh>lambdaQuery()
                .orderByDesc(VWjRyjdkh::getGmsfhm));
        DynamicDataSourceContextHolder.clearDataSourceType();

        return page.getRecords();
    }

    @Override
    public List<PoliceQuarterlyAssessment> getTargetData(int offset, int limit) {
        Page<PoliceQuarterlyAssessment> page = policeQuarterlyAssessmentService.page(Page.of(offset, limit));
        return page.getRecords();
    }

    @Override
    public PoliceQuarterlyAssessment convert(VWjRyjdkh source) {
        return converter.convert(source, PoliceQuarterlyAssessment.class);
    }

    @Override
    public Function<PoliceQuarterlyAssessment, String> getBusinessKeyGenerator() {
        return policeQuarterlyAssessment ->
                policeQuarterlyAssessment.getIdCard() + "_" +
                        policeQuarterlyAssessment.getAssessmentYear() + "_" +
                        policeQuarterlyAssessment.getAssessmentQuarter() + "_" +
                        policeQuarterlyAssessment.getAssessmentUnit();
    }

    @Override
    public void batchInsert(List<PoliceQuarterlyAssessment> records) {
        policeQuarterlyAssessmentService.saveBatch(records);
    }

    @Override
    public void batchUpdate(List<PoliceQuarterlyAssessment> records) {
        policeQuarterlyAssessmentService.updateBatchById(records);
    }

    @Override
    public void batchDelete(List<String> businessKeys) {

    }

    @Override
    public long getSourceDataCount() {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        long count = vwjRyjdkhService.count();
        DynamicDataSourceContextHolder.clearDataSourceType();
        return count;
    }

    @Override
    public long getTargetDataCount() {
        return policeQuarterlyAssessmentService.count();
    }
}
