package com.hl.orasync.sync.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.entity.PoliceProjectMaterial;
import com.hl.archive.service.PoliceProjectMaterialService;
import com.hl.common.config.datasource.DataSourceType;
import com.hl.common.config.datasource.DynamicDataSourceContextHolder;
import com.hl.orasync.domain.VWjXhjhRxgrTwzl;
import com.hl.orasync.domain.VWjXhjhRxgrXjsj;
import com.hl.orasync.service.VWjXhjhRxgrTwzlService;
import com.hl.orasync.service.VWjXhjhRxgrXjsjService;
import com.hl.orasync.sync.DataSyncProcessor;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.function.Function;

@Component
@RequiredArgsConstructor
public class PoliceProjectMaterialSyncProcessor implements DataSyncProcessor<VWjXhjhRxgrTwzl, PoliceProjectMaterial> {

    private final VWjXhjhRxgrTwzlService vWjXhjhRxgrTwzlService;

    private final PoliceProjectMaterialService policeProjectMaterialService;

    private final Converter converter;

    @Override
    public List<VWjXhjhRxgrTwzl> getSourceData(int offset, int limit) {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        Page<VWjXhjhRxgrTwzl> page = vWjXhjhRxgrTwzlService.page(Page.of(offset, limit),
                Wrappers.<VWjXhjhRxgrTwzl>lambdaQuery()
                        .orderByDesc(VWjXhjhRxgrTwzl::getXxzjbh));
        DynamicDataSourceContextHolder.clearDataSourceType();
        return page.getRecords();
    }

    @Override
    public List<PoliceProjectMaterial> getTargetData(int offset, int limit) {
        Page<PoliceProjectMaterial> page = policeProjectMaterialService.page(Page.of(offset, limit));
        return page.getRecords();
    }

    @Override
    public PoliceProjectMaterial convert(VWjXhjhRxgrTwzl source) {
        return converter.convert(source, PoliceProjectMaterial.class);
    }

    @Override
    public Function<PoliceProjectMaterial, String> getBusinessKeyGenerator() {
        return PoliceProjectMaterial::getZjbh;
    }

    @Override
    public void batchInsert(List<PoliceProjectMaterial> records) {
        policeProjectMaterialService.saveBatch(records);
    }

    @Override
    public void batchUpdate(List<PoliceProjectMaterial> records) {
        policeProjectMaterialService.updateBatchById(records);
    }

    @Override
    public void batchDelete(List<String> businessKeys) {
        // 根据业务键删除，这里需要根据实际情况实现
        // 由于业务键是组合的，可能需要特殊处理
    }

    @Override
    public long getSourceDataCount() {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        long count = vWjXhjhRxgrTwzlService.count();
        DynamicDataSourceContextHolder.clearDataSourceType();
        return count;
    }

    @Override
    public long getTargetDataCount() {
        return policeProjectMaterialService.count();
    }
}
