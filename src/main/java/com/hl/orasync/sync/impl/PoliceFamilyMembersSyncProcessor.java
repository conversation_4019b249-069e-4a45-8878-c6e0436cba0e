package com.hl.orasync.sync.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.entity.PoliceFamilyMembers;
import com.hl.archive.service.PoliceFamilyMembersService;
import com.hl.common.config.datasource.DataSourceType;
import com.hl.common.config.datasource.DynamicDataSourceContextHolder;
import com.hl.orasync.domain.VWjRyjtcy;
import com.hl.orasync.service.VWjRyjtcyService;
import com.hl.orasync.sync.DataSyncProcessor;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.function.Function;

@Component
@RequiredArgsConstructor
public class PoliceFamilyMembersSyncProcessor implements DataSyncProcessor<VWjRyjtcy, PoliceFamilyMembers> {

    private final VWjRyjtcyService vwjRyjtcyService;

    private final PoliceFamilyMembersService policeFamilyMembersService;

    private final Converter converter;

    @Override
    public List<VWjRyjtcy> getSourceData(int offset, int limit) {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));

        Page<VWjRyjtcy> page = vwjRyjtcyService.page(Page.of(offset, limit), Wrappers.<VWjRyjtcy>lambdaQuery()
                .orderByDesc(VWjRyjtcy::getGmsfhm));
        DynamicDataSourceContextHolder.clearDataSourceType();

        return page.getRecords();
    }

    @Override
    public List<PoliceFamilyMembers> getTargetData(int offset, int limit) {
        Page<PoliceFamilyMembers> page = policeFamilyMembersService.page(Page.of(offset, limit));
        return page.getRecords();
    }

    @Override
    public PoliceFamilyMembers convert(VWjRyjtcy source) {
        return converter.convert(source, PoliceFamilyMembers.class);
    }

    @Override
    public Function<PoliceFamilyMembers, String> getBusinessKeyGenerator() {
        return policeFamilyMembers ->
                policeFamilyMembers.getIdCard() + "_" +
                        policeFamilyMembers.getRelationship() + "_" +
                        policeFamilyMembers.getMemberName();
    }

    @Override
    public void batchInsert(List<PoliceFamilyMembers> records) {
        policeFamilyMembersService.saveBatch(records);
    }

    @Override
    public void batchUpdate(List<PoliceFamilyMembers> records) {
        policeFamilyMembersService.updateBatchById(records);
    }

    @Override
    public void batchDelete(List<String> businessKeys) {

    }

    @Override
    public long getSourceDataCount() {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        long count = vwjRyjtcyService.count();
        DynamicDataSourceContextHolder.clearDataSourceType();
        return count;
    }

    @Override
    public long getTargetDataCount() {
        return policeFamilyMembersService.count();
    }
}
