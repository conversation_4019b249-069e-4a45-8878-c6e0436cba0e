package com.hl.orasync.sync.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.entity.PoliceMomentSubmission;
import com.hl.archive.domain.entity.PoliceMomentSubmissionVideo;
import com.hl.archive.service.PoliceMomentSubmissionVideoService;
import com.hl.common.config.datasource.DataSourceType;
import com.hl.common.config.datasource.DynamicDataSourceContextHolder;
import com.hl.orasync.domain.VWjJcsjScsb;
import com.hl.orasync.domain.VWjJcsjScsbWj;
import com.hl.orasync.service.VWjJcsjScsbWjService;
import com.hl.orasync.sync.DataSyncProcessor;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.function.Function;

@Component
@RequiredArgsConstructor

public class PoliceMomentSubmissionVideoSyncProcessor
        implements DataSyncProcessor<VWjJcsjScsbWj, PoliceMomentSubmissionVideo> {

    private final Converter converter;

    private final VWjJcsjScsbWjService vWjJcsjScsbWjService;

    private final PoliceMomentSubmissionVideoService policeMomentSubmissionVideoService;

    @Override
    public List<VWjJcsjScsbWj> getSourceData(int offset, int limit) {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        Page<VWjJcsjScsbWj> page = vWjJcsjScsbWjService.page(Page.of(offset, limit),
                Wrappers.<VWjJcsjScsbWj>lambdaQuery()
                        .orderByDesc(VWjJcsjScsbWj::getXxzjbh));
        DynamicDataSourceContextHolder.clearDataSourceType();
        return page.getRecords();
    }

    @Override
    public List<PoliceMomentSubmissionVideo> getTargetData(int offset, int limit) {
        Page<PoliceMomentSubmissionVideo> page = policeMomentSubmissionVideoService.page(Page.of(offset, limit));
        return page.getRecords();
    }

    @Override
    public PoliceMomentSubmissionVideo convert(VWjJcsjScsbWj source) {
        return converter.convert(source, PoliceMomentSubmissionVideo.class);
    }

    @Override
    public Function<PoliceMomentSubmissionVideo, String> getBusinessKeyGenerator() {
       return PoliceMomentSubmissionVideo::getZjbh;
    }

    @Override
    public void batchInsert(List<PoliceMomentSubmissionVideo> records) {
        policeMomentSubmissionVideoService.saveBatch(records);
    }

    @Override
    public void batchUpdate(List<PoliceMomentSubmissionVideo> records) {
        policeMomentSubmissionVideoService.updateBatchById(records);

    }

    @Override
    public void batchDelete(List<String> businessKeys) {

    }

    @Override
    public long getSourceDataCount() {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        long count = vWjJcsjScsbWjService.count();
        DynamicDataSourceContextHolder.clearDataSourceType();
        return count;
    }

    @Override
    public long getTargetDataCount() {
        return policeMomentSubmissionVideoService.count();
    }
}
