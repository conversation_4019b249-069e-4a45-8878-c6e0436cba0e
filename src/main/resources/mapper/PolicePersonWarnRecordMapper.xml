<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.archive.mapper.PolicePersonWarnRecordMapper">
  <resultMap id="BaseResultMap" type="com.hl.archive.domain.entity.PolicePersonWarnRecord">
    <!--@mbg.generated-->
    <!--@Table police_person_warn_record-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="id_card" jdbcType="VARCHAR" property="idCard" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="police_number" jdbcType="VARCHAR" property="policeNumber" />
    <result column="organization_id" jdbcType="VARCHAR" property="organizationId" />
    <result column="warn_time" jdbcType="TIMESTAMP" property="warnTime" />
    <result column="warn_type" jdbcType="VARCHAR" property="warnType" />
    <result column="data_key" jdbcType="VARCHAR" property="dataKey" />
    <result column="description" jdbcType="LONGVARCHAR" property="description" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
    <result column="is_deleted" jdbcType="BOOLEAN" property="isDeleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, id_card, `name`, police_number, organization_id, warn_time, warn_type, data_key, 
    description, create_by, create_at, update_by, update_at, is_deleted
  </sql>
</mapper>