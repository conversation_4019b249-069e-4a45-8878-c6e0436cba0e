<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.archive.mapper.PoliceHeartToHeartVisitMapper">
  <resultMap id="BaseResultMap" type="com.hl.archive.domain.entity.PoliceHeartToHeartVisit">
    <!--@mbg.generated-->
    <!--@Table police_heart_to_heart_visit-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="location" jdbcType="VARCHAR" property="location" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="form" jdbcType="VARCHAR" property="form" />
    <result column="visitor_name" jdbcType="VARCHAR" property="visitorName" />
    <result column="visitor_unit" jdbcType="VARCHAR" property="visitorUnit" />
    <result column="visitor_position" jdbcType="VARCHAR" property="visitorPosition" />
    <result column="visitor_id_card" jdbcType="VARCHAR" property="visitorIdCard" />
    <result column="visitee_name" jdbcType="VARCHAR" property="visiteeName" />
    <result column="visitee_unit" jdbcType="VARCHAR" property="visiteeUnit" />
    <result column="visitee_position" jdbcType="VARCHAR" property="visiteePosition" />
    <result column="visitee_id_card" jdbcType="VARCHAR" property="visiteeIdCard" />
    <result column="content" jdbcType="LONGVARCHAR" property="content" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, start_time, end_time, `location`, reason, form, visitor_name, visitor_unit, visitor_position, 
    visitor_id_card, visitee_name, visitee_unit, visitee_position, visitee_id_card, content, 
    is_deleted, created_at, updated_at, created_by, updated_by
  </sql>
</mapper>