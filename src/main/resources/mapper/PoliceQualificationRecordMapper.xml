<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.archive.mapper.PoliceQualificationRecordMapper">
  <resultMap id="BaseResultMap" type="com.hl.archive.domain.entity.PoliceQualificationRecord">
    <!--@mbg.generated-->
    <!--@Table police_qualification_record-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="id_card" jdbcType="VARCHAR" property="idCard" />
    <result column="organization_id" jdbcType="VARCHAR" property="organizationId" />
    <result column="category" jdbcType="VARCHAR" property="category" />
    <result column="project" jdbcType="VARCHAR" property="project" />
    <result column="score" jdbcType="VARCHAR" property="score" />
    <result column="issue_date" jdbcType="DATE" property="issueDate" />
    <result column="expire_date" jdbcType="DATE" property="expireDate" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, `name`, id_card, organization_id, category, project, score, issue_date, expire_date, 
    `status`, created_by, created_at, updated_by, updated_at, is_deleted
  </sql>
</mapper>